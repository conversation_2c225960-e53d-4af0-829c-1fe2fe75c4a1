-------------------------------------
Translated Report (Full Report Below)
-------------------------------------

Process:               Python [49118]
Path:                  /Library/Frameworks/Python.framework/Versions/3.12/Resources/Python.app/Contents/MacOS/Python
Identifier:            org.python.python
Version:               3.12.3 (3.12.3)
Code Type:             ARM-64 (Native)
Parent Process:        bash [49109]
Responsible:           Terminal [2789]
User ID:               501

Date/Time:             2025-07-11 14:31:34.4110 -0500
OS Version:            macOS 15.4.1 (24E263)
Report Version:        12
Anonymous UUID:        8383FD8A-F8E1-CADF-AAE9-0A00DC35B13A

Sleep/Wake UUID:       7E2B3702-C27B-4166-A17A-AD8BA0B299D6

Time Awake Since Boot: 230000 seconds
Time Since Wake:       217324 seconds

System Integrity Protection: enabled

Crashed Thread:        0  Dispatch queue: com.apple.main-thread

Exception Type:        EXC_BAD_ACCESS (SIGBUS)
Exception Codes:       EXC_ARM_DA_ALIGN at 0x000000000bad4007
Exception Codes:       0x0000000000000101, 0x000000000bad4007

Termination Reason:    Namespace SIGNAL, Code 10 Bus error: 10
Terminating Process:   Python [49118]

VM Region Info: 0xbad4007 is not in any region.  Bytes before following region: 4102963193
      REGION TYPE                    START - END         [ VSIZE] PRT/MAX SHRMOD  REGION DETAIL
      UNUSED SPACE AT START
--->  
      __TEXT                      1003b8000-1003bc000    [   16K] r-x/r-x SM=COW  /Library/Frameworks/Python.framework/Versions/3.12/Resources/Python.app/Contents/MacOS/Python

Thread 0 Crashed::  Dispatch queue: com.apple.main-thread
0   libsystem_kernel.dylib        	       0x193934388 __pthread_kill + 8
1   libsystem_pthread.dylib       	       0x19396d88c pthread_kill + 296
2   libsystem_c.dylib             	       0x19383ed04 raise + 32
3   Python                        	       0x1016cb9c4 faulthandler_fatal_error + 384
4   libsystem_platform.dylib      	       0x1939a7624 _sigtramp + 56
5   ImageIO                       	       0x19f4bb01c PNGReadPlugin::InitializePluginData(IIOImageReadSession*, IIODictionary*, IIODictionary*, CGImageMetadata*, CGColorSpace**, ReadPluginData&, PNGPluginData&, __CFDictionary*) + 824
6   ImageIO                       	       0x19f4b3334 IIOReadPlugin::callInitialize() + 412
7   ImageIO                       	       0x19f4b30cc IIO_Reader::initImageAtOffset(CGImagePlugin*, unsigned long, unsigned long, unsigned long) + 164
8   ImageIO                       	       0x19f4b0660 IIOImageSource::makeImagePlus(unsigned long, IIODictionary*) + 536
9   ImageIO                       	       0x19f526874 IIOImageSource::createImageAtIndex(unsigned long, IIODictionary*, int*) + 104
10  ImageIO                       	       0x19f4bd0cc CGImageSourceCreateImageAtIndex + 460
11  HIServices                    	       0x19a4e9aec setCursorFromBundle + 1240
12  HIServices                    	       0x19a4e8eec CoreCursorSetAndReturnSeed + 204
13  AppKit                        	       0x1977f5ab0 -[NSCursor _reallySet] + 616
14  AppKit                        	       0x1977f57d0 -[NSCursor set] + 144
15  libgdk-3.0.dylib              	       0x103392eec _gdk_display_set_window_under_pointer + 76
16  libgdk-3.0.dylib              	       0x103394364 _gdk_windowing_got_event + 2824
17  libgdk-3.0.dylib              	       0x1033a0160 _gdk_quartz_display_queue_events + 292
18  libgdk-3.0.dylib              	       0x1033a21b4 gdk_event_dispatch + 32
19  libglib-2.0.0.dylib           	       0x101db949c g_main_context_dispatch_unlocked + 236
20  libglib-2.0.0.dylib           	       0x101db97ac g_main_context_iterate_unlocked + 484
21  libglib-2.0.0.dylib           	       0x101db980c g_main_context_iteration + 60
22  libgio-2.0.0.dylib            	       0x102f900d8 g_application_run + 516
23  libffi.dylib                  	       0x1a5db9050 ffi_call_SYSV + 80
24  libffi.dylib                  	       0x1a5dc1af0 ffi_call_int + 1220
25  _gi.cpython-312-darwin.so     	       0x101bfb4f0 pygi_invoke_c_callable + 2296
26  _gi.cpython-312-darwin.so     	       0x101bfc604 pygi_function_cache_invoke + 52
27  Python                        	       0x101626d24 _PyEval_EvalFrameDefault + 54472
28  Python                        	       0x1016194c0 PyEval_EvalCode + 304
29  Python                        	       0x101614f1c builtin_exec + 472
30  Python                        	       0x10154e0a4 cfunction_vectorcall_FASTCALL_KEYWORDS + 92
31  Python                        	       0x101624e9c _PyEval_EvalFrameDefault + 46656
32  Python                        	       0x1016c52f0 pymain_run_module + 200
33  Python                        	       0x1016c4c30 Py_RunMain + 1192
34  Python                        	       0x1016c4e98 pymain_main + 40
35  Python                        	       0x1016c5018 Py_BytesMain + 40
36  dyld                          	       0x1935ceb4c start + 6000

Thread 1:
0   libsystem_kernel.dylib        	       0x19392f1c8 __semwait_signal + 8
1   libsystem_c.dylib             	       0x19380b6f4 nanosleep + 220
2   Python                        	       0x10173c2c0 time_sleep + 176
3   Python                        	       0x101625c5c _PyEval_EvalFrameDefault + 50176
4   Python                        	       0x1014e3f2c method_vectorcall + 368
5   Python                        	       0x101626d24 _PyEval_EvalFrameDefault + 54472
6   Python                        	       0x1014e3f2c method_vectorcall + 368
7   Python                        	       0x1017397cc thread_run + 160
8   Python                        	       0x1016af5fc pythread_wrapper + 48
9   libsystem_pthread.dylib       	       0x19396dc0c _pthread_start + 136
10  libsystem_pthread.dylib       	       0x193968b80 thread_start + 8

Thread 2:
0   libsystem_pthread.dylib       	       0x193968b6c start_wqthread + 0

Thread 3:
0   libsystem_pthread.dylib       	       0x193968b6c start_wqthread + 0

Thread 4:
0   libsystem_pthread.dylib       	       0x193968b6c start_wqthread + 0

Thread 5:: pool-spawner
0   libsystem_kernel.dylib        	       0x19392f3cc __psynch_cvwait + 8
1   libsystem_pthread.dylib       	       0x19396e0e0 _pthread_cond_wait + 984
2   libglib-2.0.0.dylib           	       0x101ddd21c g_cond_wait + 44
3   libglib-2.0.0.dylib           	       0x101d8ec74 g_async_queue_pop_intern_unlocked + 104
4   libglib-2.0.0.dylib           	       0x101dde310 g_thread_pool_spawn_thread + 112
5   libglib-2.0.0.dylib           	       0x101ddd5dc g_thread_proxy + 84
6   libsystem_pthread.dylib       	       0x19396dc0c _pthread_start + 136
7   libsystem_pthread.dylib       	       0x193968b80 thread_start + 8

Thread 6:: gmain
0   libsystem_kernel.dylib        	       0x193936c2c __select + 8
1   libglib-2.0.0.dylib           	       0x101dc7208 g_poll + 436
2   libglib-2.0.0.dylib           	       0x101db9744 g_main_context_iterate_unlocked + 380
3   libglib-2.0.0.dylib           	       0x101db980c g_main_context_iteration + 60
4   libglib-2.0.0.dylib           	       0x101dba7a0 glib_worker_main + 48
5   libglib-2.0.0.dylib           	       0x101ddd5dc g_thread_proxy + 84
6   libsystem_pthread.dylib       	       0x19396dc0c _pthread_start + 136
7   libsystem_pthread.dylib       	       0x193968b80 thread_start + 8

Thread 7:: pool-0
0   libsystem_kernel.dylib        	       0x19392f3cc __psynch_cvwait + 8
1   libsystem_pthread.dylib       	       0x19396e10c _pthread_cond_wait + 1028
2   libglib-2.0.0.dylib           	       0x101dddd78 g_cond_wait_until + 124
3   libglib-2.0.0.dylib           	       0x101d8ec68 g_async_queue_pop_intern_unlocked + 92
4   libglib-2.0.0.dylib           	       0x101d8eda4 g_async_queue_timeout_pop + 60
5   libglib-2.0.0.dylib           	       0x101ddedc4 g_thread_pool_thread_proxy + 384
6   libglib-2.0.0.dylib           	       0x101ddd5dc g_thread_proxy + 84
7   libsystem_pthread.dylib       	       0x19396dc0c _pthread_start + 136
8   libsystem_pthread.dylib       	       0x193968b80 thread_start + 8

Thread 8:
0   libsystem_kernel.dylib        	       0x193936c2c __select + 8
1   readline.cpython-312-darwin.so	       0x100f422e0 readline_until_enter_or_signal + 256
2   readline.cpython-312-darwin.so	       0x100f40cd0 call_readline + 128
3   Python                        	       0x1014b240c PyOS_Readline + 408
4   Python                        	       0x1016162ac builtin_input + 2224
5   Python                        	       0x10154df9c cfunction_vectorcall_FASTCALL + 96
6   Python                        	       0x101624e9c _PyEval_EvalFrameDefault + 46656
7   Python                        	       0x1014e3f2c method_vectorcall + 368
8   Python                        	       0x1017397cc thread_run + 160
9   Python                        	       0x1016af5fc pythread_wrapper + 48
10  libsystem_pthread.dylib       	       0x19396dc0c _pthread_start + 136
11  libsystem_pthread.dylib       	       0x193968b80 thread_start + 8

Thread 9:
0   libsystem_kernel.dylib        	       0x193934498 poll + 8
1   _socket.cpython-312-darwin.so 	       0x100dcc5a8 internal_select + 100
2   _socket.cpython-312-darwin.so 	       0x100dcc300 sock_call_ex + 432
3   _socket.cpython-312-darwin.so 	       0x100dcce58 sock_recv_guts + 56
4   _socket.cpython-312-darwin.so 	       0x100dcae20 sock_recv + 108
5   Python                        	       0x1014f0ba0 method_vectorcall_VARARGS + 132
6   Python                        	       0x101624e9c _PyEval_EvalFrameDefault + 46656
7   Python                        	       0x1014e3f2c method_vectorcall + 368
8   Python                        	       0x1017397cc thread_run + 160
9   Python                        	       0x1016af5fc pythread_wrapper + 48
10  libsystem_pthread.dylib       	       0x19396dc0c _pthread_start + 136
11  libsystem_pthread.dylib       	       0x193968b80 thread_start + 8

Thread 10:
0   libsystem_pthread.dylib       	       0x193968b6c start_wqthread + 0

Thread 11:: com.apple.NSEventThread
0   libsystem_kernel.dylib        	       0x19392bc34 mach_msg2_trap + 8
1   libsystem_kernel.dylib        	       0x19393e308 mach_msg2_internal + 76
2   libsystem_kernel.dylib        	       0x193934764 mach_msg_overwrite + 484
3   libsystem_kernel.dylib        	       0x19392bfa8 mach_msg + 24
4   CoreFoundation                	       0x193a58f8c __CFRunLoopServiceMachPort + 160
5   CoreFoundation                	       0x193a578a8 __CFRunLoopRun + 1208
6   CoreFoundation                	       0x193a56d68 CFRunLoopRunSpecific + 572
7   AppKit                        	       0x1977cd818 _NSEventThread + 140
8   libsystem_pthread.dylib       	       0x19396dc0c _pthread_start + 136
9   libsystem_pthread.dylib       	       0x193968b80 thread_start + 8

Thread 12:: CVDisplayLink
0   libsystem_kernel.dylib        	       0x19392f3cc __psynch_cvwait + 8
1   libsystem_pthread.dylib       	       0x19396e10c _pthread_cond_wait + 1028
2   CoreVideo                     	       0x19d0271f4 CVDisplayLink::waitUntil(unsigned long long) + 336
3   CoreVideo                     	       0x19d0262dc CVDisplayLink::runIOThread() + 500
4   libsystem_pthread.dylib       	       0x19396dc0c _pthread_start + 136
5   libsystem_pthread.dylib       	       0x193968b80 thread_start + 8

Thread 13:
0   libsystem_kernel.dylib        	       0x193936c2c __select + 8
1   libglib-2.0.0.dylib           	       0x101dc7208 g_poll + 436
2   libgdk-3.0.dylib              	       0x1033a2834 select_thread_func + 244
3   libsystem_pthread.dylib       	       0x19396dc0c _pthread_start + 136
4   libsystem_pthread.dylib       	       0x193968b80 thread_start + 8


Thread 0 crashed with ARM Thread State (64-bit):
    x0: 0x0000000000000000   x1: 0x0000000000000000   x2: 0x0000000000000001   x3: 0x000000011808fa98
    x4: 0x0000000000000061   x5: 0x0000000000000061   x6: 0x0000000107b0a6c8   x7: 0x00006000037a5a40
    x8: 0xf4026bd09996372b   x9: 0xf4026bd29b123bab  x10: 0xcccccccccccccccd  x11: 0x000000000000000a
   x12: 0x000000011808fa53  x13: 0x0000000000000000  x14: 0x0000000000000033  x15: 0x00000000fffffff0
   x16: 0x0000000000000148  x17: 0x00000002038b0ac0  x18: 0x0000000000000000  x19: 0x000000000000000a
   x20: 0x0000000000000103  x21: 0x0000000202840d60  x22: 0x00000001019a51a8  x23: 0x0000000000000022
   x24: 0x0000000000000001  x25: 0x00000001018df700  x26: 0x0000000000000000  x27: 0x000000000010574f
   x28: 0x0000000000000001   fp: 0x000000011808fad0   lr: 0x000000019396d88c
    sp: 0x000000011808fab0   pc: 0x0000000193934388 cpsr: 0x40001000
   far: 0x0000000000000000  esr: 0x56000080  Address size fault

Binary Images:
       0x1003b8000 -        0x1003bbfff org.python.python (3.12.3) <69908f75-6985-3fbe-b13d-0a4c1db1e0ef> /Library/Frameworks/Python.framework/Versions/3.12/Resources/Python.app/Contents/MacOS/Python
       0x10145c000 -        0x101843fff org.python.python (3.12.3, (c) 2001-2023 Python Software Foundation.) <0c88e2ed-0ca8-374b-9942-812cae5f6030> /Library/Frameworks/Python.framework/Versions/3.12/Python
       0x1003fc000 -        0x10041bfff liblzma.5.dylib (*) <6b34bd7f-9320-3883-9a5d-e8542c9b17ca> /opt/homebrew/*/liblzma.5.dylib
       0x100460000 -        0x100483fff libpng16.16.dylib (*) <f279e392-5936-3ad4-a017-e5eb186625e8> /opt/homebrew/*/libpng16.16.dylib
       0x1005a4000 -        0x100607fff libtiff.6.dylib (*) <52a00bff-3e7f-3ab2-99b1-f449c7683e29> /opt/homebrew/*/libtiff.6.dylib
       0x100440000 -        0x100447fff libgif.7.2.0.dylib (*) <2a522a63-bebb-3a8e-b090-cbb336833e37> /opt/homebrew/*/libgif.7.2.0.dylib
       0x100628000 -        0x100683fff libjpeg.8.3.2.dylib (*) <905d524c-0642-3b18-95ed-cd3a79553001> /opt/homebrew/*/libjpeg.8.3.2.dylib
       0x10069c000 -        0x1006dffff libGL.1.dylib (*) <ea90d270-8633-3ea9-90b4-db2c00753407> /opt/homebrew/*/libGL.1.dylib
       0x100714000 -        0x10073ffff libglapi.0.dylib (*) <c5e9ebe0-632f-3906-91de-45bffe3ea2df> /opt/homebrew/*/libglapi.0.dylib
       0x100864000 -        0x100937fff libX11.6.dylib (*) <2665be26-d15f-3a64-b1c7-eeec879eea3c> /opt/homebrew/*/libX11.6.dylib
       0x100520000 -        0x10052bfff libxcb-glx.0.0.0.dylib (*) <1c7b46d4-a95c-33bb-8b1c-c6efbede835f> /opt/homebrew/*/libxcb-glx.0.0.0.dylib
       0x100768000 -        0x10077bfff libxcb.1.1.0.dylib (*) <8ef9b9ff-3f1e-39a6-bec1-e7d10db8e6cb> /opt/homebrew/*/libxcb.1.1.0.dylib
       0x10042c000 -        0x10042ffff libX11-xcb.1.dylib (*) <428d1551-0f79-34c4-a682-78db1439dd66> /opt/homebrew/*/libX11-xcb.1.dylib
       0x100548000 -        0x100553fff libXext.6.dylib (*) <fd706b27-845c-3b66-9744-6dd8d34f9ffa> /opt/homebrew/*/libXext.6.dylib
       0x1004a4000 -        0x1004a7fff libXau.6.dylib (*) <d53ca926-d7dd-3428-8a81-578f76379c5a> /opt/homebrew/*/libXau.6.dylib
       0x100568000 -        0x10056bfff libXdmcp.6.dylib (*) <11368a67-e3a3-3d6f-ae03-78456881a92e> /opt/homebrew/*/libXdmcp.6.dylib
       0x100960000 -        0x1009ebfff libzstd.1.5.7.dylib (*) <afd03688-5fac-3b3d-96aa-9d88c034ff23> /opt/homebrew/*/libzstd.1.5.7.dylib
       0x10081c000 -        0x10081ffff _heapq.cpython-312-darwin.so (*) <9d6f5d03-f453-36e4-8d4e-897abb8840a2> /Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_heapq.cpython-312-darwin.so
       0x100830000 -        0x100833fff _queue.cpython-312-darwin.so (*) <5977c619-61a8-3011-90d8-979a68ede0ea> /Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_queue.cpython-312-darwin.so
       0x1007f4000 -        0x1007fbfff zlib.cpython-312-darwin.so (*) <d41b6e7e-62c0-3dca-8a4d-a5d709188d47> /Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/zlib.cpython-312-darwin.so
       0x100844000 -        0x100847fff _md5.cpython-312-darwin.so (*) <9c8bd0ff-e802-39ed-b5c0-61dd66e59e76> /Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_md5.cpython-312-darwin.so
       0x100d48000 -        0x100d53fff math.cpython-312-darwin.so (*) <4549d567-7cc2-340c-91ad-de63607f52b0> /Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/math.cpython-312-darwin.so
       0x100d30000 -        0x100d33fff _bisect.cpython-312-darwin.so (*) <878dfb33-5839-3252-ba17-b3ac6e6af559> /Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_bisect.cpython-312-darwin.so
       0x100d68000 -        0x100d6bfff _random.cpython-312-darwin.so (*) <71398eef-8493-3dd1-ae5c-56d7215008c8> /Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_random.cpython-312-darwin.so
       0x100d08000 -        0x100d0ffff _sha2.cpython-312-darwin.so (*) <d3de0d5e-0bac-3241-aca6-8392c15017ac> /Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_sha2.cpython-312-darwin.so
       0x100dc4000 -        0x100dd3fff _socket.cpython-312-darwin.so (*) <b6ba59b9-6b87-3760-b4ae-e42de771ea9c> /Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_socket.cpython-312-darwin.so
       0x100de8000 -        0x100deffff select.cpython-312-darwin.so (*) <11c3d88b-3ae7-3ff5-972c-ce3af3352ba8> /Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/select.cpython-312-darwin.so
       0x100f04000 -        0x100f0ffff array.cpython-312-darwin.so (*) <5894f67d-c88f-3a26-b826-d1ab1296fa99> /Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/array.cpython-312-darwin.so
       0x100f24000 -        0x100f2bfff _struct.cpython-312-darwin.so (*) <bdecdf4c-37da-3643-ae44-c0d4da4e4611> /Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_struct.cpython-312-darwin.so
       0x100dac000 -        0x100db3fff binascii.cpython-312-darwin.so (*) <668f331c-0c46-34ad-b662-782d41407fb9> /Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/binascii.cpython-312-darwin.so
       0x100d7c000 -        0x100d8ffff _datetime.cpython-312-darwin.so (*) <b0329b34-32c6-3eac-b164-ce66a1702a9e> /Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_datetime.cpython-312-darwin.so
       0x100fd4000 -        0x100ffbfff pyexpat.cpython-312-darwin.so (*) <235f5e42-437c-3a34-93ba-4b762174c092> /Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/pyexpat.cpython-312-darwin.so
       0x100fa0000 -        0x100fb3fff _ctypes.cpython-312-darwin.so (*) <5a5e32ed-94a8-3781-bf76-6ed789122e96> /Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_ctypes.cpython-312-darwin.so
       0x100f68000 -        0x100f6bfff _bz2.cpython-312-darwin.so (*) <c06482b4-7b9e-3ea3-87ba-2a19821ff549> /Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_bz2.cpython-312-darwin.so
       0x1010ac000 -        0x1010dbfff _lzma.cpython-312-darwin.so (*) <7873221e-c849-349b-a09b-ad57e772d172> /Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_lzma.cpython-312-darwin.so
       0x100f7c000 -        0x100f7ffff termios.cpython-312-darwin.so (*) <2dc3446c-e44d-38e8-9ed9-1b7a36a019a4> /Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/termios.cpython-312-darwin.so
       0x100f40000 -        0x100f43fff readline.cpython-312-darwin.so (*) <27079f80-2dd2-39af-9c32-5276503f3fc1> /Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/readline.cpython-312-darwin.so
       0x101044000 -        0x101047fff resource.cpython-312-darwin.so (*) <6d006df5-650a-343e-9585-5b1666846652> /Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/resource.cpython-312-darwin.so
       0x101058000 -        0x10105bfff mmap.cpython-312-darwin.so (*) <dbe04907-e47c-3bcc-80bb-f89e270d60c3> /Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/mmap.cpython-312-darwin.so
       0x10106c000 -        0x101083fff _pickle.cpython-312-darwin.so (*) <4fb7c3a9-e46c-36fa-a3a1-ab23e116a6e9> /Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_pickle.cpython-312-darwin.so
       0x10101c000 -        0x101023fff _json.cpython-312-darwin.so (*) <b15899af-81f3-3820-9493-5a7f79e9d2cf> /Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_json.cpython-312-darwin.so
       0x101bdc000 -        0x101c0ffff _gi.cpython-312-darwin.so (*) <a38b1810-f562-30c7-8c35-dc4235c5ade1> /opt/homebrew/*/_gi.cpython-312-darwin.so
       0x101d88000 -        0x101e83fff libglib-2.0.0.dylib (*) <d4b3f626-4f1e-3e73-a309-77cd208fef96> /opt/homebrew/*/libglib-2.0.0.dylib
       0x101c38000 -        0x101c5ffff libgirepository-2.0.0.dylib (*) <b48b7c76-8a74-3566-8ff2-cf9e77a1cdfa> /opt/homebrew/*/libgirepository-2.0.0.dylib
       0x101c80000 -        0x101cbbfff libgobject-2.0.0.dylib (*) <240d0a14-b408-390b-95c7-41aaf4191393> /opt/homebrew/*/libgobject-2.0.0.dylib
       0x101ce8000 -        0x101d0ffff libintl.8.dylib (*) <f9485a9d-d20a-3fc3-b68f-9ebd5a52cac9> /opt/homebrew/*/libintl.8.dylib
       0x101f68000 -        0x101fe3fff libpcre2-8.0.dylib (*) <a9da24ef-74d4-397b-ab16-1d18e55a653b> /opt/homebrew/*/libpcre2-8.0.dylib
       0x1013f4000 -        0x1013f7fff libgmodule-2.0.0.dylib (*) <4cf1e38d-c2d2-33e5-a4df-98008f2c32f6> /opt/homebrew/*/libgmodule-2.0.0.dylib
       0x101098000 -        0x10109bfff fcntl.cpython-312-darwin.so (*) <6d487eff-2806-3e3e-b9cd-dfc163f7742e> /Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/fcntl.cpython-312-darwin.so
       0x101430000 -        0x101433fff _posixsubprocess.cpython-312-darwin.so (*) <3588d723-6a8f-3c99-9dee-eebe4dce2303> /Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_posixsubprocess.cpython-312-darwin.so
       0x101d24000 -        0x101d3ffff _ssl.cpython-312-darwin.so (*) <52736c90-1229-3d93-bb53-dea27eeca442> /Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_ssl.cpython-312-darwin.so
       0x1021d4000 -        0x102263fff libssl.3.dylib (*) <75c023dd-f3c8-3787-9419-d1bfe1d4e69d> /opt/homebrew/*/libssl.3.dylib
       0x102730000 -        0x102a4ffff libcrypto.3.dylib (*) <054ba906-efe8-3158-b3fd-5a1a38a10f88> /opt/homebrew/*/libcrypto.3.dylib
       0x101444000 -        0x101447fff _opcode.cpython-312-darwin.so (*) <fd5cbf9c-a4bf-35d9-b137-38378b67078c> /Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_opcode.cpython-312-darwin.so
       0x101d60000 -        0x101d63fff _contextvars.cpython-312-darwin.so (*) <af576142-86f9-3ae4-b229-c847c3dfe8d8> /Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_contextvars.cpython-312-darwin.so
       0x101408000 -        0x101413fff _asyncio.cpython-312-darwin.so (*) <bb2d8cc2-421f-3572-9456-07652a4a8ec8> /Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_asyncio.cpython-312-darwin.so
       0x101ed8000 -        0x101edbfff _gi_cairo.cpython-312-darwin.so (*) <426cb2c1-1166-30fd-9d4a-77de9be459de> /opt/homebrew/*/_gi_cairo.cpython-312-darwin.so
       0x1024d0000 -        0x1025a3fff libcairo.2.dylib (*) <812bcdd9-2c24-3689-8680-e77ee8bb5288> /opt/homebrew/*/libcairo.2.dylib
       0x101eec000 -        0x101ef3fff libcairo-gobject.2.dylib (*) <c8a70ff5-2655-341d-aa62-98f707016f3a> /opt/homebrew/*/libcairo-gobject.2.dylib
       0x1020f8000 -        0x10212bfff libfontconfig.1.dylib (*) <4fc45b59-72db-3f65-b917-eee9edeb0899> /opt/homebrew/*/libfontconfig.1.dylib
       0x1025f0000 -        0x10266bfff libfreetype.6.dylib (*) <cfd169d0-526f-3a45-b5db-836906fec3d7> /opt/homebrew/*/libfreetype.6.dylib
       0x101f1c000 -        0x101f23fff libXrender.1.dylib (*) <db90a0df-d06c-369f-a5ac-17eb5b56f20d> /opt/homebrew/*/libXrender.1.dylib
       0x102144000 -        0x10214bfff libxcb-render.0.0.0.dylib (*) <476a0562-8f0d-3135-a2fe-edccd58f3588> /opt/homebrew/*/libxcb-render.0.0.0.dylib
       0x101f04000 -        0x101f07fff libxcb-shm.0.0.0.dylib (*) <a1c61032-dc79-3bd0-a91e-0eafb93838cb> /opt/homebrew/*/libxcb-shm.0.0.0.dylib
       0x102690000 -        0x102707fff libpixman-1.0.dylib (*) <bb560dbd-afcc-3acd-a017-720296ff9cf1> /opt/homebrew/*/libpixman-1.0.dylib
       0x1023b0000 -        0x1023cbfff _cairo.cpython-312-darwin.so (*) <c1a957bc-e121-3f22-950f-88cfa89c71ee> /opt/homebrew/*/_cairo.cpython-312-darwin.so
       0x102f04000 -        0x10303ffff libgio-2.0.0.dylib (*) <8da68695-2b6e-364f-8dcb-4f431a322747> /opt/homebrew/*/libgio-2.0.0.dylib
       0x102d78000 -        0x102dbbfff libpango-1.0.0.dylib (*) <9d7ad548-d90f-31c0-a896-4ad95b4beed6> /opt/homebrew/*/libpango-1.0.0.dylib
       0x102d3c000 -        0x102d57fff libfribidi.0.dylib (*) <dd2ba6d9-be7f-3a9a-a790-384d5d8e799b> /opt/homebrew/*/libfribidi.0.dylib
       0x103230000 -        0x1032effff libharfbuzz.0.dylib (*) <3d359291-b602-39fc-8112-8876e0ebaec3> /opt/homebrew/*/libharfbuzz.0.dylib
       0x102e10000 -        0x102e23fff libgraphite2.3.2.1.dylib (*) <dadd250f-e2ba-36f3-b960-ea391395d1e7> /opt/homebrew/*/libgraphite2.3.2.1.dylib
       0x102e6c000 -        0x102e8bfff libgdk_pixbuf-2.0.0.dylib (*) <3cf5959f-cde6-3bca-a37c-96a0b5803449> /opt/homebrew/*/libgdk_pixbuf-2.0.0.dylib
       0x103368000 -        0x1033cffff libgdk-3.0.dylib (*) <d329d053-3e33-3268-96a9-27eb39859657> /opt/homebrew/*/libgdk-3.0.dylib
       0x10340c000 -        0x103487fff libepoxy.0.dylib (*) <beda4d43-da84-364e-81b2-ddd817f4e8f9> /opt/homebrew/*/libepoxy.0.dylib
       0x102e34000 -        0x102e43fff libpangocairo-1.0.0.dylib (*) <eb459207-6d4f-3931-9bc2-92ebc36f1744> /opt/homebrew/*/libpangocairo-1.0.0.dylib
       0x102ea4000 -        0x102eb3fff libpangoft2-1.0.0.dylib (*) <b0eea268-a2e2-3780-ad05-1a62ba750a4c> /opt/homebrew/*/libpangoft2-1.0.0.dylib
       0x112d0c000 -        0x1132ebfff libgtk-3.0.dylib (*) <94f03258-240f-315f-8d5b-9cad4fffcbf2> /opt/homebrew/*/libgtk-3.0.dylib
       0x1044a8000 -        0x1044bffff libatk-1.0.0.dylib (*) <2d32b2b3-8abe-3fb7-b0ca-99e0615ce54c> /opt/homebrew/*/libatk-1.0.0.dylib
       0x10448c000 -        0x104497fff libobjc-trampolines.dylib (*) <fb6f2685-3c65-37ec-8ee3-8adba6e1995f> /usr/lib/libobjc-trampolines.dylib
       0x104da8000 -        0x104daffff libffi-trampolines.dylib (*) <b24c3233-64b9-38c7-ac36-6c0aaea81bcb> /usr/lib/libffi-trampolines.dylib
       0x104ddc000 -        0x104ddffff _multiprocessing.cpython-312-darwin.so (*) <fbf6f2ad-887f-3168-bd73-5a642adeaf0e> /Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_multiprocessing.cpython-312-darwin.so
       0x117000000 -        0x117733fff com.apple.AGXMetalG15X-M1 (325.34.1) <784cb929-6269-363a-ace2-5fc27766e255> /System/Library/Extensions/AGXMetalG15X_M1.bundle/Contents/MacOS/AGXMetalG15X_M1
       0x107fcc000 -        0x107fd3fff _hashlib.cpython-312-darwin.so (*) <bddf68b9-4cac-336d-9ec9-d8c1046abd72> /Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_hashlib.cpython-312-darwin.so
       0x107fe8000 -        0x107feffff _blake2.cpython-312-darwin.so (*) <3d8f998a-34fe-323e-8248-abd18c10f0ff> /Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_blake2.cpython-312-darwin.so
       0x112a48000 -        0x112a4bfff _scproxy.cpython-312-darwin.so (*) <53a7260c-b9d6-39f7-ad60-3c1d723f86f0> /Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_scproxy.cpython-312-darwin.so
       0x112a20000 -        0x112a2bfff _elementtree.cpython-312-darwin.so (*) <8b59c7d8-11ed-3d70-b71f-c3fbe59b62c2> /Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_elementtree.cpython-312-darwin.so
       0x122000000 -        0x12210ffff unicodedata.cpython-312-darwin.so (*) <1b7769b5-1004-387f-9644-23a60609adb9> /Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/unicodedata.cpython-312-darwin.so
       0x19392b000 -        0x19396630b libsystem_kernel.dylib (*) <225cb279-20a9-381d-a163-d2be263f5327> /usr/lib/system/libsystem_kernel.dylib
       0x193967000 -        0x193973a47 libsystem_pthread.dylib (*) <8d27ec9a-d919-31a4-8df8-31a2fd2e593c> /usr/lib/system/libsystem_pthread.dylib
       0x1937fe000 -        0x19387f46f libsystem_c.dylib (*) <20ebe22e-66e2-3556-b70b-54a04e8363a8> /usr/lib/system/libsystem_c.dylib
       0x1939a4000 -        0x1939abb4f libsystem_platform.dylib (*) <27ed40c8-6b18-3266-93a7-387fb33ca186> /usr/lib/system/libsystem_platform.dylib
       0x19f4a4000 -        0x19f82277f com.apple.ImageIO (3.3.0) <55325e86-d64d-30b6-86f7-b5ab96f6c65b> /System/Library/Frameworks/ImageIO.framework/Versions/A/ImageIO
       0x19a4e1000 -        0x19a54d37f com.apple.HIServices (1.22) <fcb9ea71-5118-331b-9070-ba311437eafb> /System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/HIServices.framework/Versions/A/HIServices
       0x19766f000 -        0x198affdff com.apple.AppKit (6.9) <1d6df541-4a74-3a0e-8a8f-15aee6f93da9> /System/Library/Frameworks/AppKit.framework/Versions/C/AppKit
       0x1a5db1000 -        0x1a5dc27af libffi.dylib (*) <cf39cd82-9b61-339a-9432-7e6733409585> /usr/lib/libffi.dylib
       0x1935c8000 -        0x193662a83 dyld (*) <aca43a8d-6369-3a2c-af92-3d4c458523d6> /usr/lib/dyld
               0x0 - 0xffffffffffffffff ??? (*) <00000000-0000-0000-0000-000000000000> ???
       0x193974000 -        0x1939a3b33 libdyld.dylib (*) <e41473d4-d8b6-30f4-8646-e82b51e1b43e> /usr/lib/system/libdyld.dylib
       0x1939dc000 -        0x193f1afff com.apple.CoreFoundation (6.9) <39e0f63a-3ab8-39e9-97f8-333cde9a7ba4> /System/Library/Frameworks/CoreFoundation.framework/Versions/A/CoreFoundation
       0x19d024000 -        0x19d084ddf com.apple.CoreVideo (1.8) <8912f801-7326-344e-a079-b05c0fd5d007> /System/Library/Frameworks/CoreVideo.framework/Versions/A/CoreVideo

External Modification Summary:
  Calls made by other processes targeting this process:
    task_for_pid: 0
    thread_create: 0
    thread_set_state: 0
  Calls made by this process:
    task_for_pid: 0
    thread_create: 0
    thread_set_state: 0
  Calls made by all processes on this machine:
    task_for_pid: 0
    thread_create: 0
    thread_set_state: 0

VM Region Summary:
ReadOnly portion of Libraries: Total=1.7G resident=0K(0%) swapped_out_or_unallocated=1.7G(100%)
Writable regions: Total=2.5G written=899K(0%) resident=899K(0%) swapped_out=0K(0%) unallocated=2.5G(100%)

                                VIRTUAL   REGION 
REGION TYPE                        SIZE    COUNT (non-coalesced) 
===========                     =======  ======= 
Activity Tracing                   256K        1 
CG image                            16K        1 
ColorSync                          544K       28 
CoreAnimation                      320K       20 
CoreGraphics                        64K        4 
CoreUI image data                  192K        1 
Foundation                          16K        1 
Kernel Alloc Once                   32K        1 
MALLOC                             2.4G       87 
MALLOC guard page                  288K       18 
MALLOC_LARGE (reserved)           22.1M        1         reserved VM address space (unallocated)
Mach message                        16K        1 
STACK GUARD                        208K       13 
Stack                             69.4M       14 
Stack Guard                         16K        1 
VM_ALLOCATE                       15.2M       30 
__AUTH                            5333K      683 
__AUTH_CONST                      75.7M      921 
__CTF                               824        1 
__DATA                            27.6M      978 
__DATA_CONST                      29.1M     1011 
__DATA_DIRTY                      2760K      335 
__FONT_DATA                        2352        1 
__INFO_FILTER                         8        1 
__LINKEDIT                       632.8M       88 
__OBJC_RO                         61.3M        1 
__OBJC_RW                         2391K        1 
__TEXT                             1.1G     1033 
__TPRO_CONST                       128K        2 
dyld private memory               1024K        8 
mapped file                      238.9M       45 
page table in kernel               899K        1 
shared memory                     1392K       15 
===========                     =======  ======= 
TOTAL                              4.6G     5347 
TOTAL, minus reserved VM space     4.6G     5347 



-----------
Full Report
-----------

{"app_name":"Python","timestamp":"2025-07-11 14:31:35.00 -0500","app_version":"3.12.3","slice_uuid":"69908f75-6985-3fbe-b13d-0a4c1db1e0ef","build_version":"3.12.3","platform":1,"bundleID":"org.python.python","share_with_app_devs":1,"is_first_party":0,"bug_type":"309","os_version":"macOS 15.4.1 (24E263)","roots_installed":0,"name":"Python","incident_id":"F76E7957-33A1-4B59-BB38-B1D4EE24C6B3"}
{
  "uptime" : 230000,
  "procRole" : "Foreground",
  "version" : 2,
  "userID" : 501,
  "deployVersion" : 210,
  "modelCode" : "Mac15,10",
  "coalitionID" : 30460,
  "osVersion" : {
    "train" : "macOS 15.4.1",
    "build" : "24E263",
    "releaseType" : "User"
  },
  "captureTime" : "2025-07-11 14:31:34.4110 -0500",
  "codeSigningMonitor" : 1,
  "incident" : "F76E7957-33A1-4B59-BB38-B1D4EE24C6B3",
  "pid" : 49118,
  "translated" : false,
  "cpuType" : "ARM-64",
  "roots_installed" : 0,
  "bug_type" : "309",
  "procLaunch" : "2025-07-11 14:31:25.4043 -0500",
  "procStartAbsTime" : 5621971765069,
  "procExitAbsTime" : 5622187848624,
  "procName" : "Python",
  "procPath" : "\/Library\/Frameworks\/Python.framework\/Versions\/3.12\/Resources\/Python.app\/Contents\/MacOS\/Python",
  "bundleInfo" : {"CFBundleShortVersionString":"3.12.3","CFBundleVersion":"3.12.3","CFBundleIdentifier":"org.python.python"},
  "storeInfo" : {"deviceIdentifierForVendor":"2523CCCE-6C6F-5726-86A8-5B8295D32E5C","thirdParty":true},
  "parentProc" : "bash",
  "parentPid" : 49109,
  "coalitionName" : "com.apple.Terminal",
  "crashReporterKey" : "8383FD8A-F8E1-CADF-AAE9-0A00DC35B13A",
  "appleIntelligenceStatus" : {"state":"available"},
  "responsiblePid" : 2789,
  "responsibleProc" : "Terminal",
  "codeSigningID" : "org.python.python",
  "codeSigningTeamID" : "BMM5U3QVKW",
  "codeSigningFlags" : 570491393,
  "codeSigningValidationCategory" : 6,
  "codeSigningTrustLevel" : 4294967295,
  "codeSigningAuxiliaryInfo" : 0,
  "instructionByteStream" : {"beforePC":"fyMD1f17v6n9AwCRm+D\/l78DAJH9e8Go\/w9f1sADX9YQKYDSARAA1A==","atPC":"AwEAVH8jA9X9e7+p\/QMAkZDg\/5e\/AwCR\/XvBqP8PX9bAA1\/WcAqA0g=="},
  "bootSessionUUID" : "6DCCF449-C89C-46B2-9E01-AF7F398A0BE2",
  "wakeTime" : 217324,
  "sleepWakeUUID" : "7E2B3702-C27B-4166-A17A-AD8BA0B299D6",
  "sip" : "enabled",
  "vmRegionInfo" : "0xbad4007 is not in any region.  Bytes before following region: 4102963193\n      REGION TYPE                    START - END         [ VSIZE] PRT\/MAX SHRMOD  REGION DETAIL\n      UNUSED SPACE AT START\n--->  \n      __TEXT                      1003b8000-1003bc000    [   16K] r-x\/r-x SM=COW  \/Library\/Frameworks\/Python.framework\/Versions\/3.12\/Resources\/Python.app\/Contents\/MacOS\/Python",
  "exception" : {"codes":"0x0000000000000101, 0x000000000bad4007","rawCodes":[257,195903495],"type":"EXC_BAD_ACCESS","signal":"SIGBUS","subtype":"EXC_ARM_DA_ALIGN at 0x000000000bad4007"},
  "termination" : {"flags":0,"code":10,"namespace":"SIGNAL","indicator":"Bus error: 10","byProc":"Python","byPid":49118},
  "vmregioninfo" : "0xbad4007 is not in any region.  Bytes before following region: 4102963193\n      REGION TYPE                    START - END         [ VSIZE] PRT\/MAX SHRMOD  REGION DETAIL\n      UNUSED SPACE AT START\n--->  \n      __TEXT                      1003b8000-1003bc000    [   16K] r-x\/r-x SM=COW  \/Library\/Frameworks\/Python.framework\/Versions\/3.12\/Resources\/Python.app\/Contents\/MacOS\/Python",
  "extMods" : {"caller":{"thread_create":0,"thread_set_state":0,"task_for_pid":0},"system":{"thread_create":0,"thread_set_state":0,"task_for_pid":0},"targeted":{"thread_create":0,"thread_set_state":0,"task_for_pid":0},"warnings":0},
  "faultingThread" : 0,
  "threads" : [{"triggered":true,"id":14912654,"threadState":{"x":[{"value":0},{"value":0},{"value":1},{"value":4698208920},{"value":97},{"value":97},{"value":4423984840},{"value":105553174616640},{"value":17582734438881965867},{"value":17582734447496805291},{"value":14757395258967641293},{"value":10},{"value":4698208851},{"value":0},{"value":51},{"value":4294967280},{"value":328},{"value":8649378496},{"value":0},{"value":10},{"value":259},{"value":8632143200,"symbolLocation":224,"symbol":"_main_thread"},{"value":4321857960,"symbolLocation":0,"symbol":"_PyRuntime"},{"value":34},{"value":1},{"value":4321048320,"symbolLocation":0,"symbol":"faulthandler_handlers"},{"value":0},{"value":1070927},{"value":1}],"flavor":"ARM_THREAD_STATE64","lr":{"value":6771103884},"cpsr":{"value":1073745920},"fp":{"value":4698208976},"sp":{"value":4698208944},"esr":{"value":1442840704,"description":" Address size fault"},"pc":{"value":6770869128,"matchesCrashFrame":1},"far":{"value":0}},"queue":"com.apple.main-thread","frames":[{"imageOffset":37768,"symbol":"__pthread_kill","symbolLocation":8,"imageIndex":87},{"imageOffset":26764,"symbol":"pthread_kill","symbolLocation":296,"imageIndex":88},{"imageOffset":265476,"symbol":"raise","symbolLocation":32,"imageIndex":89},{"imageOffset":2554308,"symbol":"faulthandler_fatal_error","symbolLocation":384,"imageIndex":1},{"imageOffset":13860,"symbol":"_sigtramp","symbolLocation":56,"imageIndex":90},{"imageOffset":94236,"symbol":"PNGReadPlugin::InitializePluginData(IIOImageReadSession*, IIODictionary*, IIODictionary*, CGImageMetadata*, CGColorSpace**, ReadPluginData&, PNGPluginData&, __CFDictionary*)","symbolLocation":824,"imageIndex":91},{"imageOffset":62260,"symbol":"IIOReadPlugin::callInitialize()","symbolLocation":412,"imageIndex":91},{"imageOffset":61644,"symbol":"IIO_Reader::initImageAtOffset(CGImagePlugin*, unsigned long, unsigned long, unsigned long)","symbolLocation":164,"imageIndex":91},{"imageOffset":50784,"symbol":"IIOImageSource::makeImagePlus(unsigned long, IIODictionary*)","symbolLocation":536,"imageIndex":91},{"imageOffset":534644,"symbol":"IIOImageSource::createImageAtIndex(unsigned long, IIODictionary*, int*)","symbolLocation":104,"imageIndex":91},{"imageOffset":102604,"symbol":"CGImageSourceCreateImageAtIndex","symbolLocation":460,"imageIndex":91},{"imageOffset":35564,"symbol":"setCursorFromBundle","symbolLocation":1240,"imageIndex":92},{"imageOffset":32492,"symbol":"CoreCursorSetAndReturnSeed","symbolLocation":204,"imageIndex":92},{"imageOffset":1600176,"symbol":"-[NSCursor _reallySet]","symbolLocation":616,"imageIndex":93},{"imageOffset":1599440,"symbol":"-[NSCursor set]","symbolLocation":144,"imageIndex":93},{"imageOffset":175852,"symbol":"_gdk_display_set_window_under_pointer","symbolLocation":76,"imageIndex":72},{"imageOffset":181092,"symbol":"_gdk_windowing_got_event","symbolLocation":2824,"imageIndex":72},{"imageOffset":229728,"symbol":"_gdk_quartz_display_queue_events","symbolLocation":292,"imageIndex":72},{"imageOffset":238004,"symbol":"gdk_event_dispatch","symbolLocation":32,"imageIndex":72},{"imageOffset":201884,"symbol":"g_main_context_dispatch_unlocked","symbolLocation":236,"imageIndex":42},{"imageOffset":202668,"symbol":"g_main_context_iterate_unlocked","symbolLocation":484,"imageIndex":42},{"imageOffset":202764,"symbol":"g_main_context_iteration","symbolLocation":60,"imageIndex":42},{"imageOffset":573656,"symbol":"g_application_run","symbolLocation":516,"imageIndex":66},{"imageOffset":32848,"symbol":"ffi_call_SYSV","symbolLocation":80,"imageIndex":94},{"imageOffset":68336,"symbol":"ffi_call_int","symbolLocation":1220,"imageIndex":94},{"imageOffset":128240,"symbol":"pygi_invoke_c_callable","symbolLocation":2296,"imageIndex":41},{"imageOffset":132612,"symbol":"pygi_function_cache_invoke","symbolLocation":52,"imageIndex":41},{"imageOffset":1879332,"symbol":"_PyEval_EvalFrameDefault","symbolLocation":54472,"imageIndex":1},{"imageOffset":1823936,"symbol":"PyEval_EvalCode","symbolLocation":304,"imageIndex":1},{"imageOffset":1806108,"symbol":"builtin_exec","symbolLocation":472,"imageIndex":1},{"imageOffset":991396,"symbol":"cfunction_vectorcall_FASTCALL_KEYWORDS","symbolLocation":92,"imageIndex":1},{"imageOffset":1871516,"symbol":"_PyEval_EvalFrameDefault","symbolLocation":46656,"imageIndex":1},{"imageOffset":2527984,"symbol":"pymain_run_module","symbolLocation":200,"imageIndex":1},{"imageOffset":2526256,"symbol":"Py_RunMain","symbolLocation":1192,"imageIndex":1},{"imageOffset":2526872,"symbol":"pymain_main","symbolLocation":40,"imageIndex":1},{"imageOffset":2527256,"symbol":"Py_BytesMain","symbolLocation":40,"imageIndex":1},{"imageOffset":27468,"symbol":"start","symbolLocation":6000,"imageIndex":95}]},{"id":14912674,"frames":[{"imageOffset":16840,"symbol":"__semwait_signal","symbolLocation":8,"imageIndex":87},{"imageOffset":55028,"symbol":"nanosleep","symbolLocation":220,"imageIndex":89},{"imageOffset":3015360,"symbol":"time_sleep","symbolLocation":176,"imageIndex":1},{"imageOffset":1875036,"symbol":"_PyEval_EvalFrameDefault","symbolLocation":50176,"imageIndex":1},{"imageOffset":556844,"symbol":"method_vectorcall","symbolLocation":368,"imageIndex":1},{"imageOffset":1879332,"symbol":"_PyEval_EvalFrameDefault","symbolLocation":54472,"imageIndex":1},{"imageOffset":556844,"symbol":"method_vectorcall","symbolLocation":368,"imageIndex":1},{"imageOffset":3004364,"symbol":"thread_run","symbolLocation":160,"imageIndex":1},{"imageOffset":2438652,"symbol":"pythread_wrapper","symbolLocation":48,"imageIndex":1},{"imageOffset":27660,"symbol":"_pthread_start","symbolLocation":136,"imageIndex":88},{"imageOffset":7040,"symbol":"thread_start","symbolLocation":8,"imageIndex":88}],"threadState":{"x":[{"value":4},{"value":0},{"value":1},{"value":1},{"value":1},{"value":0},{"value":512},{"value":6184822296},{"value":8632180568,"symbolLocation":0,"symbol":"clock_sem"},{"value":3},{"value":17},{"value":2199023256066},{"value":5376},{"value":2199023256064},{"value":0},{"value":0},{"value":334},{"value":8649378544},{"value":0},{"value":0},{"value":6184823424},{"value":5729620240},{"value":1000000000},{"value":6771142904,"symbolLocation":0,"symbol":"_tlv_get_addr"},{"value":234257981896916},{"value":4321860680,"symbolLocation":2720,"symbol":"_PyRuntime"},{"value":4299801104},{"value":2},{"value":18446744073709551613}],"flavor":"ARM_THREAD_STATE64","lr":{"value":6769653492},"cpsr":{"value":2684358656},"fp":{"value":6184823392},"sp":{"value":6184823344},"esr":{"value":1442840704,"description":" Address size fault"},"pc":{"value":6770848200},"far":{"value":0}}},{"id":14912675,"frames":[{"imageOffset":7020,"symbol":"start_wqthread","symbolLocation":0,"imageIndex":88}],"threadState":{"x":[{"value":6185398272},{"value":5123},{"value":6184861696},{"value":0},{"value":409604},{"value":18446744073709551615},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0}],"flavor":"ARM_THREAD_STATE64","lr":{"value":0},"cpsr":{"value":4096},"fp":{"value":0},"sp":{"value":6185398272},"esr":{"value":1442840704,"description":" Address size fault"},"pc":{"value":6771084140},"far":{"value":0}}},{"id":14912676,"frames":[{"imageOffset":7020,"symbol":"start_wqthread","symbolLocation":0,"imageIndex":88}],"threadState":{"x":[{"value":6185971712},{"value":4099},{"value":6185435136},{"value":0},{"value":409604},{"value":18446744073709551615},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0}],"flavor":"ARM_THREAD_STATE64","lr":{"value":0},"cpsr":{"value":4096},"fp":{"value":0},"sp":{"value":6185971712},"esr":{"value":1442840704,"description":" Address size fault"},"pc":{"value":6771084140},"far":{"value":0}}},{"id":14912681,"frames":[{"imageOffset":7020,"symbol":"start_wqthread","symbolLocation":0,"imageIndex":88}],"threadState":{"x":[{"value":6186545152},{"value":17155},{"value":6186008576},{"value":0},{"value":409604},{"value":18446744073709551615},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0}],"flavor":"ARM_THREAD_STATE64","lr":{"value":0},"cpsr":{"value":4096},"fp":{"value":0},"sp":{"value":6186545152},"esr":{"value":1442840704,"description":" Address size fault"},"pc":{"value":6771084140},"far":{"value":0}}},{"id":14912683,"name":"pool-spawner","threadState":{"x":[{"value":260},{"value":0},{"value":256},{"value":0},{"value":0},{"value":160},{"value":0},{"value":0},{"value":6187118168},{"value":0},{"value":0},{"value":2},{"value":2},{"value":0},{"value":0},{"value":0},{"value":305},{"value":8649378424},{"value":0},{"value":105553153193664},{"value":105553175244464},{"value":6187118816},{"value":0},{"value":0},{"value":256},{"value":257},{"value":512},{"value":0},{"value":0}],"flavor":"ARM_THREAD_STATE64","lr":{"value":6771106016},"cpsr":{"value":1610616832},"fp":{"value":6187118288},"sp":{"value":6187118144},"esr":{"value":1442840704,"description":" Address size fault"},"pc":{"value":6770848716},"far":{"value":0}},"frames":[{"imageOffset":17356,"symbol":"__psynch_cvwait","symbolLocation":8,"imageIndex":87},{"imageOffset":28896,"symbol":"_pthread_cond_wait","symbolLocation":984,"imageIndex":88},{"imageOffset":348700,"symbol":"g_cond_wait","symbolLocation":44,"imageIndex":42},{"imageOffset":27764,"symbol":"g_async_queue_pop_intern_unlocked","symbolLocation":104,"imageIndex":42},{"imageOffset":353040,"symbol":"g_thread_pool_spawn_thread","symbolLocation":112,"imageIndex":42},{"imageOffset":349660,"symbol":"g_thread_proxy","symbolLocation":84,"imageIndex":42},{"imageOffset":27660,"symbol":"_pthread_start","symbolLocation":136,"imageIndex":88},{"imageOffset":7040,"symbol":"thread_start","symbolLocation":8,"imageIndex":88}]},{"id":14912684,"name":"gmain","threadState":{"x":[{"value":4},{"value":0},{"value":6187691392},{"value":6187691264},{"value":0},{"value":3822356750},{"value":4304},{"value":0},{"value":6187691656},{"value":1000},{"value":0},{"value":2},{"value":1},{"value":105553170796752},{"value":2095104},{"value":2043},{"value":93},{"value":8649386992},{"value":0},{"value":105553170796760},{"value":4294967295},{"value":105553170796752},{"value":6},{"value":1},{"value":105553170796760},{"value":5},{"value":6187691264},{"value":6187691392},{"value":6187691520}],"flavor":"ARM_THREAD_STATE64","lr":{"value":4326191624},"cpsr":{"value":2684358656},"fp":{"value":6187691760},"sp":{"value":6187691248},"esr":{"value":1442840704,"description":" Address size fault"},"pc":{"value":6770879532},"far":{"value":0}},"frames":[{"imageOffset":48172,"symbol":"__select","symbolLocation":8,"imageIndex":87},{"imageOffset":258568,"symbol":"g_poll","symbolLocation":436,"imageIndex":42},{"imageOffset":202564,"symbol":"g_main_context_iterate_unlocked","symbolLocation":380,"imageIndex":42},{"imageOffset":202764,"symbol":"g_main_context_iteration","symbolLocation":60,"imageIndex":42},{"imageOffset":206752,"symbol":"glib_worker_main","symbolLocation":48,"imageIndex":42},{"imageOffset":349660,"symbol":"g_thread_proxy","symbolLocation":84,"imageIndex":42},{"imageOffset":27660,"symbol":"_pthread_start","symbolLocation":136,"imageIndex":88},{"imageOffset":7040,"symbol":"thread_start","symbolLocation":8,"imageIndex":88}]},{"id":14912777,"name":"pool-0","threadState":{"x":[{"value":260},{"value":0},{"value":0},{"value":0},{"value":0},{"value":160},{"value":15},{"value":0},{"value":1},{"value":0},{"value":0},{"value":2},{"value":2},{"value":0},{"value":0},{"value":0},{"value":305},{"value":8649378424},{"value":0},{"value":105553153193536},{"value":105553175244416},{"value":1},{"value":0},{"value":15},{"value":0},{"value":1},{"value":256},{"value":4326981632},{"value":1000}],"flavor":"ARM_THREAD_STATE64","lr":{"value":6771106060},"cpsr":{"value":2684358656},"fp":{"value":6332673696},"sp":{"value":6332673552},"esr":{"value":1442840704,"description":" Address size fault"},"pc":{"value":6770848716},"far":{"value":0}},"frames":[{"imageOffset":17356,"symbol":"__psynch_cvwait","symbolLocation":8,"imageIndex":87},{"imageOffset":28940,"symbol":"_pthread_cond_wait","symbolLocation":1028,"imageIndex":88},{"imageOffset":351608,"symbol":"g_cond_wait_until","symbolLocation":124,"imageIndex":42},{"imageOffset":27752,"symbol":"g_async_queue_pop_intern_unlocked","symbolLocation":92,"imageIndex":42},{"imageOffset":28068,"symbol":"g_async_queue_timeout_pop","symbolLocation":60,"imageIndex":42},{"imageOffset":355780,"symbol":"g_thread_pool_thread_proxy","symbolLocation":384,"imageIndex":42},{"imageOffset":349660,"symbol":"g_thread_proxy","symbolLocation":84,"imageIndex":42},{"imageOffset":27660,"symbol":"_pthread_start","symbolLocation":136,"imageIndex":88},{"imageOffset":7040,"symbol":"thread_start","symbolLocation":8,"imageIndex":88}]},{"id":14912779,"frames":[{"imageOffset":48172,"symbol":"__select","symbolLocation":8,"imageIndex":87},{"imageOffset":8928,"symbol":"readline_until_enter_or_signal","symbolLocation":256,"imageIndex":36},{"imageOffset":3280,"symbol":"call_readline","symbolLocation":128,"imageIndex":36},{"imageOffset":353292,"symbol":"PyOS_Readline","symbolLocation":408,"imageIndex":1},{"imageOffset":1811116,"symbol":"builtin_input","symbolLocation":2224,"imageIndex":1},{"imageOffset":991132,"symbol":"cfunction_vectorcall_FASTCALL","symbolLocation":96,"imageIndex":1},{"imageOffset":1871516,"symbol":"_PyEval_EvalFrameDefault","symbolLocation":46656,"imageIndex":1},{"imageOffset":556844,"symbol":"method_vectorcall","symbolLocation":368,"imageIndex":1},{"imageOffset":3004364,"symbol":"thread_run","symbolLocation":160,"imageIndex":1},{"imageOffset":2438652,"symbol":"pythread_wrapper","symbolLocation":48,"imageIndex":1},{"imageOffset":27660,"symbol":"_pthread_start","symbolLocation":136,"imageIndex":88},{"imageOffset":7040,"symbol":"thread_start","symbolLocation":8,"imageIndex":88}],"threadState":{"x":[{"value":4},{"value":0},{"value":0},{"value":0},{"value":0},{"value":6771331552,"symbolLocation":352,"symbol":"_platform_memset_pattern16"},{"value":5182257248},{"value":912},{"value":6349500588},{"value":8632185608,"symbolLocation":0,"symbol":"errno"},{"value":2},{"value":1099511627776},{"value":4294967293},{"value":0},{"value":0},{"value":0},{"value":93},{"value":8649378184},{"value":0},{"value":1},{"value":0},{"value":0},{"value":4310990848},{"value":4310974144},{"value":4322317472,"symbolLocation":0,"symbol":"PyOS_InputHook"},{"value":6349499008},{"value":8613779112,"symbolLocation":0,"symbol":"rl_instream"},{"value":6770927688,"symbolLocation":0,"symbol":"__darwin_check_fd_set_overflow"},{"value":6349499024}],"flavor":"ARM_THREAD_STATE64","lr":{"value":4310967008},"cpsr":{"value":536875008},"fp":{"value":6349499248},"sp":{"value":6349498976},"esr":{"value":1442840704,"description":" Address size fault"},"pc":{"value":6770879532},"far":{"value":0}}},{"id":14912780,"frames":[{"imageOffset":38040,"symbol":"poll","symbolLocation":8,"imageIndex":87},{"imageOffset":34216,"symbol":"internal_select","symbolLocation":100,"imageIndex":25},{"imageOffset":33536,"symbol":"sock_call_ex","symbolLocation":432,"imageIndex":25},{"imageOffset":36440,"symbol":"sock_recv_guts","symbolLocation":56,"imageIndex":25},{"imageOffset":28192,"symbol":"sock_recv","symbolLocation":108,"imageIndex":25},{"imageOffset":609184,"symbol":"method_vectorcall_VARARGS","symbolLocation":132,"imageIndex":1},{"imageOffset":1871516,"symbol":"_PyEval_EvalFrameDefault","symbolLocation":46656,"imageIndex":1},{"imageOffset":556844,"symbol":"method_vectorcall","symbolLocation":368,"imageIndex":1},{"imageOffset":3004364,"symbol":"thread_run","symbolLocation":160,"imageIndex":1},{"imageOffset":2438652,"symbol":"pythread_wrapper","symbolLocation":48,"imageIndex":1},{"imageOffset":27660,"symbol":"_pthread_start","symbolLocation":136,"imageIndex":88},{"imageOffset":7040,"symbol":"thread_start","symbolLocation":8,"imageIndex":88}],"threadState":{"x":[{"value":4},{"value":0},{"value":1100},{"value":0},{"value":0},{"value":0},{"value":1100000000},{"value":6366325200},{"value":0},{"value":2199023256066},{"value":2199023256064},{"value":2199023256066},{"value":5376},{"value":2199023256064},{"value":0},{"value":5},{"value":230},{"value":105553171271712},{"value":0},{"value":1100},{"value":4369298704},{"value":6366325856},{"value":4309438076,"symbolLocation":0,"symbol":"sock_recv_impl"},{"value":1100000000},{"value":0},{"value":0},{"value":234258825180083},{"value":2},{"value":18446744073709551613}],"flavor":"ARM_THREAD_STATE64","lr":{"value":4309435816},"cpsr":{"value":536875008},"fp":{"value":6366325744},"sp":{"value":6366325712},"esr":{"value":1442840704,"description":" Address size fault"},"pc":{"value":6770869400},"far":{"value":0}}},{"id":14912782,"frames":[{"imageOffset":7020,"symbol":"start_wqthread","symbolLocation":0,"imageIndex":88}],"threadState":{"x":[{"value":6366900224},{"value":33539},{"value":6366363648},{"value":0},{"value":409604},{"value":18446744073709551615},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0}],"flavor":"ARM_THREAD_STATE64","lr":{"value":0},"cpsr":{"value":4096},"fp":{"value":0},"sp":{"value":6366900224},"esr":{"value":1442840704,"description":" Address size fault"},"pc":{"value":6771084140},"far":{"value":0}}},{"id":14912786,"name":"com.apple.NSEventThread","threadState":{"x":[{"value":268451845},{"value":21592279046},{"value":8589934592,"symbolLocation":16,"symbol":".compoundliteral.155"},{"value":159442070929408},{"value":0},{"value":159442070929408},{"value":2},{"value":4294967295},{"value":0},{"value":17179869184},{"value":0},{"value":2},{"value":0},{"value":0},{"value":37123},{"value":0},{"value":18446744073709551569},{"value":8649380280},{"value":0},{"value":4294967295},{"value":2},{"value":159442070929408},{"value":0},{"value":159442070929408},{"value":6367469704},{"value":8589934592,"symbolLocation":16,"symbol":".compoundliteral.155"},{"value":21592279046},{"value":18446744073709550527},{"value":4412409862}],"flavor":"ARM_THREAD_STATE64","lr":{"value":6770909960},"cpsr":{"value":4096},"fp":{"value":6367469552},"sp":{"value":6367469472},"esr":{"value":1442840704,"description":" Address size fault"},"pc":{"value":6770834484},"far":{"value":0}},"frames":[{"imageOffset":3124,"symbol":"mach_msg2_trap","symbolLocation":8,"imageIndex":87},{"imageOffset":78600,"symbol":"mach_msg2_internal","symbolLocation":76,"imageIndex":87},{"imageOffset":38756,"symbol":"mach_msg_overwrite","symbolLocation":484,"imageIndex":87},{"imageOffset":4008,"symbol":"mach_msg","symbolLocation":24,"imageIndex":87},{"imageOffset":511884,"symbol":"__CFRunLoopServiceMachPort","symbolLocation":160,"imageIndex":98},{"imageOffset":506024,"symbol":"__CFRunLoopRun","symbolLocation":1208,"imageIndex":98},{"imageOffset":503144,"symbol":"CFRunLoopRunSpecific","symbolLocation":572,"imageIndex":98},{"imageOffset":1435672,"symbol":"_NSEventThread","symbolLocation":140,"imageIndex":93},{"imageOffset":27660,"symbol":"_pthread_start","symbolLocation":136,"imageIndex":88},{"imageOffset":7040,"symbol":"thread_start","symbolLocation":8,"imageIndex":88}]},{"id":14912794,"name":"CVDisplayLink","threadState":{"x":[{"value":260},{"value":0},{"value":0},{"value":0},{"value":0},{"value":65704},{"value":0},{"value":7587125},{"value":251649},{"value":0},{"value":0},{"value":2},{"value":2},{"value":0},{"value":0},{"value":0},{"value":305},{"value":8649378424},{"value":0},{"value":5461114936},{"value":5461115000},{"value":1},{"value":7587125},{"value":0},{"value":0},{"value":251649},{"value":251904},{"value":5622187986952},{"value":0}],"flavor":"ARM_THREAD_STATE64","lr":{"value":6771106060},"cpsr":{"value":2684358656},"fp":{"value":6368046512},"sp":{"value":6368046368},"esr":{"value":1442840704,"description":" Address size fault"},"pc":{"value":6770848716},"far":{"value":0}},"frames":[{"imageOffset":17356,"symbol":"__psynch_cvwait","symbolLocation":8,"imageIndex":87},{"imageOffset":28940,"symbol":"_pthread_cond_wait","symbolLocation":1028,"imageIndex":88},{"imageOffset":12788,"symbol":"CVDisplayLink::waitUntil(unsigned long long)","symbolLocation":336,"imageIndex":99},{"imageOffset":8924,"symbol":"CVDisplayLink::runIOThread()","symbolLocation":500,"imageIndex":99},{"imageOffset":27660,"symbol":"_pthread_start","symbolLocation":136,"imageIndex":88},{"imageOffset":7040,"symbol":"thread_start","symbolLocation":8,"imageIndex":88}]},{"id":14912804,"frames":[{"imageOffset":48172,"symbol":"__select","symbolLocation":8,"imageIndex":87},{"imageOffset":258568,"symbol":"g_poll","symbolLocation":436,"imageIndex":42},{"imageOffset":239668,"symbol":"select_thread_func","symbolLocation":244,"imageIndex":72},{"imageOffset":27660,"symbol":"_pthread_start","symbolLocation":136,"imageIndex":88},{"imageOffset":7040,"symbol":"thread_start","symbolLocation":8,"imageIndex":88}],"threadState":{"x":[{"value":4},{"value":0},{"value":6368620000},{"value":6368619872},{"value":0},{"value":160},{"value":0},{"value":0},{"value":6368620264},{"value":1000},{"value":8},{"value":53876069773570},{"value":2043},{"value":2045},{"value":2621694344},{"value":2619595241},{"value":93},{"value":392},{"value":0},{"value":105553174638008},{"value":4294967295},{"value":105553174637984},{"value":9},{"value":3},{"value":105553174638008},{"value":8},{"value":6368619872},{"value":6368620000},{"value":6368620128}],"flavor":"ARM_THREAD_STATE64","lr":{"value":4326191624},"cpsr":{"value":2684358656},"fp":{"value":6368620368},"sp":{"value":6368619856},"esr":{"value":1442840704,"description":" Address size fault"},"pc":{"value":6770879532},"far":{"value":0}}}],
  "usedImages" : [
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4298866688,
    "CFBundleShortVersionString" : "3.12.3",
    "CFBundleIdentifier" : "org.python.python",
    "size" : 16384,
    "uuid" : "69908f75-6985-3fbe-b13d-0a4c1db1e0ef",
    "path" : "\/Library\/Frameworks\/Python.framework\/Versions\/3.12\/Resources\/Python.app\/Contents\/MacOS\/Python",
    "name" : "Python",
    "CFBundleVersion" : "3.12.3"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4316315648,
    "CFBundleShortVersionString" : "3.12.3, (c) 2001-2023 Python Software Foundation.",
    "CFBundleIdentifier" : "org.python.python",
    "size" : 4096000,
    "uuid" : "0c88e2ed-0ca8-374b-9942-812cae5f6030",
    "path" : "\/Library\/Frameworks\/Python.framework\/Versions\/3.12\/Python",
    "name" : "Python",
    "CFBundleVersion" : "3.12.3"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4299145216,
    "size" : 131072,
    "uuid" : "6b34bd7f-9320-3883-9a5d-e8542c9b17ca",
    "path" : "\/opt\/homebrew\/*\/liblzma.5.dylib",
    "name" : "liblzma.5.dylib"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4299554816,
    "size" : 147456,
    "uuid" : "f279e392-5936-3ad4-a017-e5eb186625e8",
    "path" : "\/opt\/homebrew\/*\/libpng16.16.dylib",
    "name" : "libpng16.16.dylib"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4300881920,
    "size" : 409600,
    "uuid" : "52a00bff-3e7f-3ab2-99b1-f449c7683e29",
    "path" : "\/opt\/homebrew\/*\/libtiff.6.dylib",
    "name" : "libtiff.6.dylib"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4299423744,
    "size" : 32768,
    "uuid" : "2a522a63-bebb-3a8e-b090-cbb336833e37",
    "path" : "\/opt\/homebrew\/*\/libgif.7.2.0.dylib",
    "name" : "libgif.7.2.0.dylib"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4301422592,
    "size" : 376832,
    "uuid" : "905d524c-0642-3b18-95ed-cd3a79553001",
    "path" : "\/opt\/homebrew\/*\/libjpeg.8.3.2.dylib",
    "name" : "libjpeg.8.3.2.dylib"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4301897728,
    "size" : 278528,
    "uuid" : "ea90d270-8633-3ea9-90b4-db2c00753407",
    "path" : "\/opt\/homebrew\/*\/libGL.1.dylib",
    "name" : "libGL.1.dylib"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4302389248,
    "size" : 180224,
    "uuid" : "c5e9ebe0-632f-3906-91de-45bffe3ea2df",
    "path" : "\/opt\/homebrew\/*\/libglapi.0.dylib",
    "name" : "libglapi.0.dylib"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4303765504,
    "size" : 868352,
    "uuid" : "2665be26-d15f-3a64-b1c7-eeec879eea3c",
    "path" : "\/opt\/homebrew\/*\/libX11.6.dylib",
    "name" : "libX11.6.dylib"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4300341248,
    "size" : 49152,
    "uuid" : "1c7b46d4-a95c-33bb-8b1c-c6efbede835f",
    "path" : "\/opt\/homebrew\/*\/libxcb-glx.0.0.0.dylib",
    "name" : "libxcb-glx.0.0.0.dylib"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4302733312,
    "size" : 81920,
    "uuid" : "8ef9b9ff-3f1e-39a6-bec1-e7d10db8e6cb",
    "path" : "\/opt\/homebrew\/*\/libxcb.1.1.0.dylib",
    "name" : "libxcb.1.1.0.dylib"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4299341824,
    "size" : 16384,
    "uuid" : "428d1551-0f79-34c4-a682-78db1439dd66",
    "path" : "\/opt\/homebrew\/*\/libX11-xcb.1.dylib",
    "name" : "libX11-xcb.1.dylib"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4300505088,
    "size" : 49152,
    "uuid" : "fd706b27-845c-3b66-9744-6dd8d34f9ffa",
    "path" : "\/opt\/homebrew\/*\/libXext.6.dylib",
    "name" : "libXext.6.dylib"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4299833344,
    "size" : 16384,
    "uuid" : "d53ca926-d7dd-3428-8a81-578f76379c5a",
    "path" : "\/opt\/homebrew\/*\/libXau.6.dylib",
    "name" : "libXau.6.dylib"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4300636160,
    "size" : 16384,
    "uuid" : "11368a67-e3a3-3d6f-ae03-78456881a92e",
    "path" : "\/opt\/homebrew\/*\/libXdmcp.6.dylib",
    "name" : "libXdmcp.6.dylib"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4304797696,
    "size" : 573440,
    "uuid" : "afd03688-5fac-3b3d-96aa-9d88c034ff23",
    "path" : "\/opt\/homebrew\/*\/libzstd.1.5.7.dylib",
    "name" : "libzstd.1.5.7.dylib"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4303470592,
    "size" : 16384,
    "uuid" : "9d6f5d03-f453-36e4-8d4e-897abb8840a2",
    "path" : "\/Library\/Frameworks\/Python.framework\/Versions\/3.12\/lib\/python3.12\/lib-dynload\/_heapq.cpython-312-darwin.so",
    "name" : "_heapq.cpython-312-darwin.so"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4303552512,
    "size" : 16384,
    "uuid" : "5977c619-61a8-3011-90d8-979a68ede0ea",
    "path" : "\/Library\/Frameworks\/Python.framework\/Versions\/3.12\/lib\/python3.12\/lib-dynload\/_queue.cpython-312-darwin.so",
    "name" : "_queue.cpython-312-darwin.so"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4303306752,
    "size" : 32768,
    "uuid" : "d41b6e7e-62c0-3dca-8a4d-a5d709188d47",
    "path" : "\/Library\/Frameworks\/Python.framework\/Versions\/3.12\/lib\/python3.12\/lib-dynload\/zlib.cpython-312-darwin.so",
    "name" : "zlib.cpython-312-darwin.so"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4303634432,
    "size" : 16384,
    "uuid" : "9c8bd0ff-e802-39ed-b5c0-61dd66e59e76",
    "path" : "\/Library\/Frameworks\/Python.framework\/Versions\/3.12\/lib\/python3.12\/lib-dynload\/_md5.cpython-312-darwin.so",
    "name" : "_md5.cpython-312-darwin.so"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4308893696,
    "size" : 49152,
    "uuid" : "4549d567-7cc2-340c-91ad-de63607f52b0",
    "path" : "\/Library\/Frameworks\/Python.framework\/Versions\/3.12\/lib\/python3.12\/lib-dynload\/math.cpython-312-darwin.so",
    "name" : "math.cpython-312-darwin.so"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4308795392,
    "size" : 16384,
    "uuid" : "878dfb33-5839-3252-ba17-b3ac6e6af559",
    "path" : "\/Library\/Frameworks\/Python.framework\/Versions\/3.12\/lib\/python3.12\/lib-dynload\/_bisect.cpython-312-darwin.so",
    "name" : "_bisect.cpython-312-darwin.so"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4309024768,
    "size" : 16384,
    "uuid" : "71398eef-8493-3dd1-ae5c-56d7215008c8",
    "path" : "\/Library\/Frameworks\/Python.framework\/Versions\/3.12\/lib\/python3.12\/lib-dynload\/_random.cpython-312-darwin.so",
    "name" : "_random.cpython-312-darwin.so"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4308631552,
    "size" : 32768,
    "uuid" : "d3de0d5e-0bac-3241-aca6-8392c15017ac",
    "path" : "\/Library\/Frameworks\/Python.framework\/Versions\/3.12\/lib\/python3.12\/lib-dynload\/_sha2.cpython-312-darwin.so",
    "name" : "_sha2.cpython-312-darwin.so"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4309401600,
    "size" : 65536,
    "uuid" : "b6ba59b9-6b87-3760-b4ae-e42de771ea9c",
    "path" : "\/Library\/Frameworks\/Python.framework\/Versions\/3.12\/lib\/python3.12\/lib-dynload\/_socket.cpython-312-darwin.so",
    "name" : "_socket.cpython-312-darwin.so"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4309549056,
    "size" : 32768,
    "uuid" : "11c3d88b-3ae7-3ff5-972c-ce3af3352ba8",
    "path" : "\/Library\/Frameworks\/Python.framework\/Versions\/3.12\/lib\/python3.12\/lib-dynload\/select.cpython-312-darwin.so",
    "name" : "select.cpython-312-darwin.so"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4310712320,
    "size" : 49152,
    "uuid" : "5894f67d-c88f-3a26-b826-d1ab1296fa99",
    "path" : "\/Library\/Frameworks\/Python.framework\/Versions\/3.12\/lib\/python3.12\/lib-dynload\/array.cpython-312-darwin.so",
    "name" : "array.cpython-312-darwin.so"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4310843392,
    "size" : 32768,
    "uuid" : "bdecdf4c-37da-3643-ae44-c0d4da4e4611",
    "path" : "\/Library\/Frameworks\/Python.framework\/Versions\/3.12\/lib\/python3.12\/lib-dynload\/_struct.cpython-312-darwin.so",
    "name" : "_struct.cpython-312-darwin.so"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4309303296,
    "size" : 32768,
    "uuid" : "668f331c-0c46-34ad-b662-782d41407fb9",
    "path" : "\/Library\/Frameworks\/Python.framework\/Versions\/3.12\/lib\/python3.12\/lib-dynload\/binascii.cpython-312-darwin.so",
    "name" : "binascii.cpython-312-darwin.so"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4309106688,
    "size" : 81920,
    "uuid" : "b0329b34-32c6-3eac-b164-ce66a1702a9e",
    "path" : "\/Library\/Frameworks\/Python.framework\/Versions\/3.12\/lib\/python3.12\/lib-dynload\/_datetime.cpython-312-darwin.so",
    "name" : "_datetime.cpython-312-darwin.so"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4311564288,
    "size" : 163840,
    "uuid" : "235f5e42-437c-3a34-93ba-4b762174c092",
    "path" : "\/Library\/Frameworks\/Python.framework\/Versions\/3.12\/lib\/python3.12\/lib-dynload\/pyexpat.cpython-312-darwin.so",
    "name" : "pyexpat.cpython-312-darwin.so"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4311351296,
    "size" : 81920,
    "uuid" : "5a5e32ed-94a8-3781-bf76-6ed789122e96",
    "path" : "\/Library\/Frameworks\/Python.framework\/Versions\/3.12\/lib\/python3.12\/lib-dynload\/_ctypes.cpython-312-darwin.so",
    "name" : "_ctypes.cpython-312-darwin.so"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4311121920,
    "size" : 16384,
    "uuid" : "c06482b4-7b9e-3ea3-87ba-2a19821ff549",
    "path" : "\/Library\/Frameworks\/Python.framework\/Versions\/3.12\/lib\/python3.12\/lib-dynload\/_bz2.cpython-312-darwin.so",
    "name" : "_bz2.cpython-312-darwin.so"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4312449024,
    "size" : 196608,
    "uuid" : "7873221e-c849-349b-a09b-ad57e772d172",
    "path" : "\/Library\/Frameworks\/Python.framework\/Versions\/3.12\/lib\/python3.12\/lib-dynload\/_lzma.cpython-312-darwin.so",
    "name" : "_lzma.cpython-312-darwin.so"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4311203840,
    "size" : 16384,
    "uuid" : "2dc3446c-e44d-38e8-9ed9-1b7a36a019a4",
    "path" : "\/Library\/Frameworks\/Python.framework\/Versions\/3.12\/lib\/python3.12\/lib-dynload\/termios.cpython-312-darwin.so",
    "name" : "termios.cpython-312-darwin.so"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4310958080,
    "size" : 16384,
    "uuid" : "27079f80-2dd2-39af-9c32-5276503f3fc1",
    "path" : "\/Library\/Frameworks\/Python.framework\/Versions\/3.12\/lib\/python3.12\/lib-dynload\/readline.cpython-312-darwin.so",
    "name" : "readline.cpython-312-darwin.so"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4312023040,
    "size" : 16384,
    "uuid" : "6d006df5-650a-343e-9585-5b1666846652",
    "path" : "\/Library\/Frameworks\/Python.framework\/Versions\/3.12\/lib\/python3.12\/lib-dynload\/resource.cpython-312-darwin.so",
    "name" : "resource.cpython-312-darwin.so"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4312104960,
    "size" : 16384,
    "uuid" : "dbe04907-e47c-3bcc-80bb-f89e270d60c3",
    "path" : "\/Library\/Frameworks\/Python.framework\/Versions\/3.12\/lib\/python3.12\/lib-dynload\/mmap.cpython-312-darwin.so",
    "name" : "mmap.cpython-312-darwin.so"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4312186880,
    "size" : 98304,
    "uuid" : "4fb7c3a9-e46c-36fa-a3a1-ab23e116a6e9",
    "path" : "\/Library\/Frameworks\/Python.framework\/Versions\/3.12\/lib\/python3.12\/lib-dynload\/_pickle.cpython-312-darwin.so",
    "name" : "_pickle.cpython-312-darwin.so"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4311859200,
    "size" : 32768,
    "uuid" : "b15899af-81f3-3820-9493-5a7f79e9d2cf",
    "path" : "\/Library\/Frameworks\/Python.framework\/Versions\/3.12\/lib\/python3.12\/lib-dynload\/_json.cpython-312-darwin.so",
    "name" : "_json.cpython-312-darwin.so"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4324179968,
    "size" : 212992,
    "uuid" : "a38b1810-f562-30c7-8c35-dc4235c5ade1",
    "path" : "\/opt\/homebrew\/*\/_gi.cpython-312-darwin.so",
    "name" : "_gi.cpython-312-darwin.so"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4325933056,
    "size" : 1032192,
    "uuid" : "d4b3f626-4f1e-3e73-a309-77cd208fef96",
    "path" : "\/opt\/homebrew\/*\/libglib-2.0.0.dylib",
    "name" : "libglib-2.0.0.dylib"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4324556800,
    "size" : 163840,
    "uuid" : "b48b7c76-8a74-3566-8ff2-cf9e77a1cdfa",
    "path" : "\/opt\/homebrew\/*\/libgirepository-2.0.0.dylib",
    "name" : "libgirepository-2.0.0.dylib"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4324851712,
    "size" : 245760,
    "uuid" : "240d0a14-b408-390b-95c7-41aaf4191393",
    "path" : "\/opt\/homebrew\/*\/libgobject-2.0.0.dylib",
    "name" : "libgobject-2.0.0.dylib"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4325277696,
    "size" : 163840,
    "uuid" : "f9485a9d-d20a-3fc3-b68f-9ebd5a52cac9",
    "path" : "\/opt\/homebrew\/*\/libintl.8.dylib",
    "name" : "libintl.8.dylib"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4327899136,
    "size" : 507904,
    "uuid" : "a9da24ef-74d4-397b-ab16-1d18e55a653b",
    "path" : "\/opt\/homebrew\/*\/libpcre2-8.0.dylib",
    "name" : "libpcre2-8.0.dylib"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4315889664,
    "size" : 16384,
    "uuid" : "4cf1e38d-c2d2-33e5-a4df-98008f2c32f6",
    "path" : "\/opt\/homebrew\/*\/libgmodule-2.0.0.dylib",
    "name" : "libgmodule-2.0.0.dylib"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4312367104,
    "size" : 16384,
    "uuid" : "6d487eff-2806-3e3e-b9cd-dfc163f7742e",
    "path" : "\/Library\/Frameworks\/Python.framework\/Versions\/3.12\/lib\/python3.12\/lib-dynload\/fcntl.cpython-312-darwin.so",
    "name" : "fcntl.cpython-312-darwin.so"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4316135424,
    "size" : 16384,
    "uuid" : "3588d723-6a8f-3c99-9dee-eebe4dce2303",
    "path" : "\/Library\/Frameworks\/Python.framework\/Versions\/3.12\/lib\/python3.12\/lib-dynload\/_posixsubprocess.cpython-312-darwin.so",
    "name" : "_posixsubprocess.cpython-312-darwin.so"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4325523456,
    "size" : 114688,
    "uuid" : "52736c90-1229-3d93-bb53-dea27eeca442",
    "path" : "\/Library\/Frameworks\/Python.framework\/Versions\/3.12\/lib\/python3.12\/lib-dynload\/_ssl.cpython-312-darwin.so",
    "name" : "_ssl.cpython-312-darwin.so"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4330438656,
    "size" : 589824,
    "uuid" : "75c023dd-f3c8-3787-9419-d1bfe1d4e69d",
    "path" : "\/opt\/homebrew\/*\/libssl.3.dylib",
    "name" : "libssl.3.dylib"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4336058368,
    "size" : 3276800,
    "uuid" : "054ba906-efe8-3158-b3fd-5a1a38a10f88",
    "path" : "\/opt\/homebrew\/*\/libcrypto.3.dylib",
    "name" : "libcrypto.3.dylib"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4316217344,
    "size" : 16384,
    "uuid" : "fd5cbf9c-a4bf-35d9-b137-38378b67078c",
    "path" : "\/Library\/Frameworks\/Python.framework\/Versions\/3.12\/lib\/python3.12\/lib-dynload\/_opcode.cpython-312-darwin.so",
    "name" : "_opcode.cpython-312-darwin.so"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4325769216,
    "size" : 16384,
    "uuid" : "af576142-86f9-3ae4-b229-c847c3dfe8d8",
    "path" : "\/Library\/Frameworks\/Python.framework\/Versions\/3.12\/lib\/python3.12\/lib-dynload\/_contextvars.cpython-312-darwin.so",
    "name" : "_contextvars.cpython-312-darwin.so"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4315971584,
    "size" : 49152,
    "uuid" : "bb2d8cc2-421f-3572-9456-07652a4a8ec8",
    "path" : "\/Library\/Frameworks\/Python.framework\/Versions\/3.12\/lib\/python3.12\/lib-dynload\/_asyncio.cpython-312-darwin.so",
    "name" : "_asyncio.cpython-312-darwin.so"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4327309312,
    "size" : 16384,
    "uuid" : "426cb2c1-1166-30fd-9d4a-77de9be459de",
    "path" : "\/opt\/homebrew\/*\/_gi_cairo.cpython-312-darwin.so",
    "name" : "_gi_cairo.cpython-312-darwin.so"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4333568000,
    "size" : 868352,
    "uuid" : "812bcdd9-2c24-3689-8680-e77ee8bb5288",
    "path" : "\/opt\/homebrew\/*\/libcairo.2.dylib",
    "name" : "libcairo.2.dylib"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4327391232,
    "size" : 32768,
    "uuid" : "c8a70ff5-2655-341d-aa62-98f707016f3a",
    "path" : "\/opt\/homebrew\/*\/libcairo-gobject.2.dylib",
    "name" : "libcairo-gobject.2.dylib"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4329537536,
    "size" : 212992,
    "uuid" : "4fc45b59-72db-3f65-b917-eee9edeb0899",
    "path" : "\/opt\/homebrew\/*\/libfontconfig.1.dylib",
    "name" : "libfontconfig.1.dylib"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4334747648,
    "size" : 507904,
    "uuid" : "cfd169d0-526f-3a45-b5db-836906fec3d7",
    "path" : "\/opt\/homebrew\/*\/libfreetype.6.dylib",
    "name" : "libfreetype.6.dylib"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4327587840,
    "size" : 32768,
    "uuid" : "db90a0df-d06c-369f-a5ac-17eb5b56f20d",
    "path" : "\/opt\/homebrew\/*\/libXrender.1.dylib",
    "name" : "libXrender.1.dylib"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4329848832,
    "size" : 32768,
    "uuid" : "476a0562-8f0d-3135-a2fe-edccd58f3588",
    "path" : "\/opt\/homebrew\/*\/libxcb-render.0.0.0.dylib",
    "name" : "libxcb-render.0.0.0.dylib"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4327489536,
    "size" : 16384,
    "uuid" : "a1c61032-dc79-3bd0-a91e-0eafb93838cb",
    "path" : "\/opt\/homebrew\/*\/libxcb-shm.0.0.0.dylib",
    "name" : "libxcb-shm.0.0.0.dylib"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4335403008,
    "size" : 491520,
    "uuid" : "bb560dbd-afcc-3acd-a017-720296ff9cf1",
    "path" : "\/opt\/homebrew\/*\/libpixman-1.0.dylib",
    "name" : "libpixman-1.0.dylib"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4332388352,
    "size" : 114688,
    "uuid" : "c1a957bc-e121-3f22-950f-88cfa89c71ee",
    "path" : "\/opt\/homebrew\/*\/_cairo.cpython-312-darwin.so",
    "name" : "_cairo.cpython-312-darwin.so"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4344266752,
    "size" : 1294336,
    "uuid" : "8da68695-2b6e-364f-8dcb-4f431a322747",
    "path" : "\/opt\/homebrew\/*\/libgio-2.0.0.dylib",
    "name" : "libgio-2.0.0.dylib"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4342644736,
    "size" : 278528,
    "uuid" : "9d7ad548-d90f-31c0-a896-4ad95b4beed6",
    "path" : "\/opt\/homebrew\/*\/libpango-1.0.0.dylib",
    "name" : "libpango-1.0.0.dylib"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4342398976,
    "size" : 114688,
    "uuid" : "dd2ba6d9-be7f-3a9a-a790-384d5d8e799b",
    "path" : "\/opt\/homebrew\/*\/libfribidi.0.dylib",
    "name" : "libfribidi.0.dylib"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4347592704,
    "size" : 786432,
    "uuid" : "3d359291-b602-39fc-8112-8876e0ebaec3",
    "path" : "\/opt\/homebrew\/*\/libharfbuzz.0.dylib",
    "name" : "libharfbuzz.0.dylib"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4343267328,
    "size" : 81920,
    "uuid" : "dadd250f-e2ba-36f3-b960-ea391395d1e7",
    "path" : "\/opt\/homebrew\/*\/libgraphite2.3.2.1.dylib",
    "name" : "libgraphite2.3.2.1.dylib"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4343644160,
    "size" : 131072,
    "uuid" : "3cf5959f-cde6-3bca-a37c-96a0b5803449",
    "path" : "\/opt\/homebrew\/*\/libgdk_pixbuf-2.0.0.dylib",
    "name" : "libgdk_pixbuf-2.0.0.dylib"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4348870656,
    "size" : 425984,
    "uuid" : "d329d053-3e33-3268-96a9-27eb39859657",
    "path" : "\/opt\/homebrew\/*\/libgdk-3.0.dylib",
    "name" : "libgdk-3.0.dylib"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4349542400,
    "size" : 507904,
    "uuid" : "beda4d43-da84-364e-81b2-ddd817f4e8f9",
    "path" : "\/opt\/homebrew\/*\/libepoxy.0.dylib",
    "name" : "libepoxy.0.dylib"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4343414784,
    "size" : 65536,
    "uuid" : "eb459207-6d4f-3931-9bc2-92ebc36f1744",
    "path" : "\/opt\/homebrew\/*\/libpangocairo-1.0.0.dylib",
    "name" : "libpangocairo-1.0.0.dylib"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4343873536,
    "size" : 65536,
    "uuid" : "b0eea268-a2e2-3780-ad05-1a62ba750a4c",
    "path" : "\/opt\/homebrew\/*\/libpangoft2-1.0.0.dylib",
    "name" : "libpangoft2-1.0.0.dylib"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4610637824,
    "size" : 6160384,
    "uuid" : "94f03258-240f-315f-8d5b-9cad4fffcbf2",
    "path" : "\/opt\/homebrew\/*\/libgtk-3.0.dylib",
    "name" : "libgtk-3.0.dylib"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4366958592,
    "size" : 98304,
    "uuid" : "2d32b2b3-8abe-3fb7-b0ca-99e0615ce54c",
    "path" : "\/opt\/homebrew\/*\/libatk-1.0.0.dylib",
    "name" : "libatk-1.0.0.dylib"
  },
  {
    "source" : "P",
    "arch" : "arm64e",
    "base" : 4366843904,
    "size" : 49152,
    "uuid" : "fb6f2685-3c65-37ec-8ee3-8adba6e1995f",
    "path" : "\/usr\/lib\/libobjc-trampolines.dylib",
    "name" : "libobjc-trampolines.dylib"
  },
  {
    "source" : "P",
    "arch" : "arm64e",
    "base" : 4376395776,
    "size" : 32768,
    "uuid" : "b24c3233-64b9-38c7-ac36-6c0aaea81bcb",
    "path" : "\/usr\/lib\/libffi-trampolines.dylib",
    "name" : "libffi-trampolines.dylib"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4376608768,
    "size" : 16384,
    "uuid" : "fbf6f2ad-887f-3168-bd73-5a642adeaf0e",
    "path" : "\/Library\/Frameworks\/Python.framework\/Versions\/3.12\/lib\/python3.12\/lib-dynload\/_multiprocessing.cpython-312-darwin.so",
    "name" : "_multiprocessing.cpython-312-darwin.so"
  },
  {
    "source" : "P",
    "arch" : "arm64e",
    "base" : 4680843264,
    "CFBundleShortVersionString" : "325.34.1",
    "CFBundleIdentifier" : "com.apple.AGXMetalG15X-M1",
    "size" : 7553024,
    "uuid" : "784cb929-6269-363a-ace2-5fc27766e255",
    "path" : "\/System\/Library\/Extensions\/AGXMetalG15X_M1.bundle\/Contents\/MacOS\/AGXMetalG15X_M1",
    "name" : "AGXMetalG15X_M1",
    "CFBundleVersion" : "325.34.1"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4428972032,
    "size" : 32768,
    "uuid" : "bddf68b9-4cac-336d-9ec9-d8c1046abd72",
    "path" : "\/Library\/Frameworks\/Python.framework\/Versions\/3.12\/lib\/python3.12\/lib-dynload\/_hashlib.cpython-312-darwin.so",
    "name" : "_hashlib.cpython-312-darwin.so"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4429086720,
    "size" : 32768,
    "uuid" : "3d8f998a-34fe-323e-8248-abd18c10f0ff",
    "path" : "\/Library\/Frameworks\/Python.framework\/Versions\/3.12\/lib\/python3.12\/lib-dynload\/_blake2.cpython-312-darwin.so",
    "name" : "_blake2.cpython-312-darwin.so"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4607737856,
    "size" : 16384,
    "uuid" : "53a7260c-b9d6-39f7-ad60-3c1d723f86f0",
    "path" : "\/Library\/Frameworks\/Python.framework\/Versions\/3.12\/lib\/python3.12\/lib-dynload\/_scproxy.cpython-312-darwin.so",
    "name" : "_scproxy.cpython-312-darwin.so"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4607574016,
    "size" : 49152,
    "uuid" : "8b59c7d8-11ed-3d70-b71f-c3fbe59b62c2",
    "path" : "\/Library\/Frameworks\/Python.framework\/Versions\/3.12\/lib\/python3.12\/lib-dynload\/_elementtree.cpython-312-darwin.so",
    "name" : "_elementtree.cpython-312-darwin.so"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4865392640,
    "size" : 1114112,
    "uuid" : "1b7769b5-1004-387f-9644-23a60609adb9",
    "path" : "\/Library\/Frameworks\/Python.framework\/Versions\/3.12\/lib\/python3.12\/lib-dynload\/unicodedata.cpython-312-darwin.so",
    "name" : "unicodedata.cpython-312-darwin.so"
  },
  {
    "source" : "P",
    "arch" : "arm64e",
    "base" : 6770831360,
    "size" : 242444,
    "uuid" : "225cb279-20a9-381d-a163-d2be263f5327",
    "path" : "\/usr\/lib\/system\/libsystem_kernel.dylib",
    "name" : "libsystem_kernel.dylib"
  },
  {
    "source" : "P",
    "arch" : "arm64e",
    "base" : 6771077120,
    "size" : 51784,
    "uuid" : "8d27ec9a-d919-31a4-8df8-31a2fd2e593c",
    "path" : "\/usr\/lib\/system\/libsystem_pthread.dylib",
    "name" : "libsystem_pthread.dylib"
  },
  {
    "source" : "P",
    "arch" : "arm64e",
    "base" : 6769598464,
    "size" : 529520,
    "uuid" : "20ebe22e-66e2-3556-b70b-54a04e8363a8",
    "path" : "\/usr\/lib\/system\/libsystem_c.dylib",
    "name" : "libsystem_c.dylib"
  },
  {
    "source" : "P",
    "arch" : "arm64e",
    "base" : 6771326976,
    "size" : 31568,
    "uuid" : "27ed40c8-6b18-3266-93a7-387fb33ca186",
    "path" : "\/usr\/lib\/system\/libsystem_platform.dylib",
    "name" : "libsystem_platform.dylib"
  },
  {
    "source" : "P",
    "arch" : "arm64e",
    "base" : 6967410688,
    "CFBundleShortVersionString" : "3.3.0",
    "CFBundleIdentifier" : "com.apple.ImageIO",
    "size" : 3663744,
    "uuid" : "55325e86-d64d-30b6-86f7-b5ab96f6c65b",
    "path" : "\/System\/Library\/Frameworks\/ImageIO.framework\/Versions\/A\/ImageIO",
    "name" : "ImageIO",
    "CFBundleVersion" : "2661.4.9"
  },
  {
    "source" : "P",
    "arch" : "arm64e",
    "base" : 6883774464,
    "CFBundleShortVersionString" : "1.22",
    "CFBundleIdentifier" : "com.apple.HIServices",
    "size" : 443264,
    "uuid" : "fcb9ea71-5118-331b-9070-ba311437eafb",
    "path" : "\/System\/Library\/Frameworks\/ApplicationServices.framework\/Versions\/A\/Frameworks\/HIServices.framework\/Versions\/A\/HIServices",
    "name" : "HIServices"
  },
  {
    "source" : "P",
    "arch" : "arm64e",
    "base" : 6835073024,
    "CFBundleShortVersionString" : "6.9",
    "CFBundleIdentifier" : "com.apple.AppKit",
    "size" : 21564928,
    "uuid" : "1d6df541-4a74-3a0e-8a8f-15aee6f93da9",
    "path" : "\/System\/Library\/Frameworks\/AppKit.framework\/Versions\/C\/AppKit",
    "name" : "AppKit",
    "CFBundleVersion" : "2575.50.27"
  },
  {
    "source" : "P",
    "arch" : "arm64e",
    "base" : 7077564416,
    "size" : 71600,
    "uuid" : "cf39cd82-9b61-339a-9432-7e6733409585",
    "path" : "\/usr\/lib\/libffi.dylib",
    "name" : "libffi.dylib"
  },
  {
    "source" : "P",
    "arch" : "arm64e",
    "base" : 6767280128,
    "size" : 633476,
    "uuid" : "aca43a8d-6369-3a2c-af92-3d4c458523d6",
    "path" : "\/usr\/lib\/dyld",
    "name" : "dyld"
  },
  {
    "size" : 0,
    "source" : "A",
    "base" : 0,
    "uuid" : "00000000-0000-0000-0000-000000000000"
  },
  {
    "source" : "P",
    "arch" : "arm64e",
    "base" : 6771130368,
    "size" : 195380,
    "uuid" : "e41473d4-d8b6-30f4-8646-e82b51e1b43e",
    "path" : "\/usr\/lib\/system\/libdyld.dylib",
    "name" : "libdyld.dylib"
  },
  {
    "source" : "P",
    "arch" : "arm64e",
    "base" : 6771556352,
    "CFBundleShortVersionString" : "6.9",
    "CFBundleIdentifier" : "com.apple.CoreFoundation",
    "size" : 5500928,
    "uuid" : "39e0f63a-3ab8-39e9-97f8-333cde9a7ba4",
    "path" : "\/System\/Library\/Frameworks\/CoreFoundation.framework\/Versions\/A\/CoreFoundation",
    "name" : "CoreFoundation",
    "CFBundleVersion" : "3423"
  },
  {
    "source" : "P",
    "arch" : "arm64e",
    "base" : 6929137664,
    "CFBundleShortVersionString" : "1.8",
    "CFBundleIdentifier" : "com.apple.CoreVideo",
    "size" : 396768,
    "uuid" : "8912f801-7326-344e-a079-b05c0fd5d007",
    "path" : "\/System\/Library\/Frameworks\/CoreVideo.framework\/Versions\/A\/CoreVideo",
    "name" : "CoreVideo",
    "CFBundleVersion" : "682.6"
  }
],
  "sharedCache" : {
  "base" : 6766444544,
  "size" : 5020123136,
  "uuid" : "bb86a9b4-8362-37b8-b028-212703af6f30"
},
  "vmSummary" : "ReadOnly portion of Libraries: Total=1.7G resident=0K(0%) swapped_out_or_unallocated=1.7G(100%)\nWritable regions: Total=2.5G written=899K(0%) resident=899K(0%) swapped_out=0K(0%) unallocated=2.5G(100%)\n\n                                VIRTUAL   REGION \nREGION TYPE                        SIZE    COUNT (non-coalesced) \n===========                     =======  ======= \nActivity Tracing                   256K        1 \nCG image                            16K        1 \nColorSync                          544K       28 \nCoreAnimation                      320K       20 \nCoreGraphics                        64K        4 \nCoreUI image data                  192K        1 \nFoundation                          16K        1 \nKernel Alloc Once                   32K        1 \nMALLOC                             2.4G       87 \nMALLOC guard page                  288K       18 \nMALLOC_LARGE (reserved)           22.1M        1         reserved VM address space (unallocated)\nMach message                        16K        1 \nSTACK GUARD                        208K       13 \nStack                             69.4M       14 \nStack Guard                         16K        1 \nVM_ALLOCATE                       15.2M       30 \n__AUTH                            5333K      683 \n__AUTH_CONST                      75.7M      921 \n__CTF                               824        1 \n__DATA                            27.6M      978 \n__DATA_CONST                      29.1M     1011 \n__DATA_DIRTY                      2760K      335 \n__FONT_DATA                        2352        1 \n__INFO_FILTER                         8        1 \n__LINKEDIT                       632.8M       88 \n__OBJC_RO                         61.3M        1 \n__OBJC_RW                         2391K        1 \n__TEXT                             1.1G     1033 \n__TPRO_CONST                       128K        2 \ndyld private memory               1024K        8 \nmapped file                      238.9M       45 \npage table in kernel               899K        1 \nshared memory                     1392K       15 \n===========                     =======  ======= \nTOTAL                              4.6G     5347 \nTOTAL, minus reserved VM space     4.6G     5347 \n",
  "legacyInfo" : {
  "threadTriggered" : {
    "queue" : "com.apple.main-thread"
  }
},
  "logWritingSignature" : "8a39a878101024a685e77d2a9265c559054f2b97",
  "trialInfo" : {
  "rollouts" : [
    {
      "rolloutId" : "64c17a9925d75a7281053d4c",
      "factorPackIds" : {
        "SIRI_AUDIO_DISABLE_MEDIA_ENTITY_SYNC" : "64d29746ad29a465b3bbeace"
      },
      "deploymentId" : 240000002
    },
    {
      "rolloutId" : "64b21a7351cbb02ce3442e4e",
      "factorPackIds" : {
        "REMINDERS_GROCERY" : "6647f0f7b6a75d3dc32993e7"
      },
      "deploymentId" : 240000042
    }
  ],
  "experiments" : [

  ]
}
}

Model: Mac15,10, BootROM 11881.101.1, proc 14:10:4 processors, 36 GB, SMC 
Graphics: Apple M3 Max, Apple M3 Max, Built-In
Display: Color LCD, 3024 x 1964 Retina, Main, MirrorOff, Online
Display: HSG1076, 1920 x 1080 (1080p FHD - Full High Definition), MirrorOff, Online
Display: EW234, 1920 x 1080 (1080p FHD - Full High Definition), MirrorOff, Online
Memory Module: LPDDR5, Micron
AirPort: spairport_wireless_card_type_wifi (0x14E4, 0x4388), wl0: Feb 27 2025 18:17:26 version 23.40.26.0.41.51.177 FWID 01-36c62c6c
IO80211_driverkit-1475.34 "IO80211_driverkit-1475.34" Mar  9 2025 20:59:13
AirPort: 
Bluetooth: Version (null), 0 services, 0 devices, 0 incoming serial ports
Network Service: Wi-Fi, AirPort, en0
USB Device: USB31Bus
USB Device: USB3.1 Hub
USB Device: USB3.0 Hub
USB Device: USB 10/100/1000 LAN
USB Device: NS1081
USB Device: USB2.0 Hub
USB Device: Extreme SSD
USB Device: USB2.0 Hub
USB Device: USB31Bus
USB Device: PSSD T7
USB Device: USB31Bus
USB Device: RTL9210B-CG
Thunderbolt Bus: MacBook Pro, Apple Inc.
Thunderbolt Bus: MacBook Pro, Apple Inc.
Thunderbolt Bus: MacBook Pro, Apple Inc.
