#!/bin/bash

# Nicotine Plus Plus - macOS Application Launcher
# Enhanced Soulseek client with bulk search functionality
# Version with crash recovery and enhanced error handling

# Enable error handling
set -e
trap 'handle_error $? $LINENO' ERR

# Error handler function
handle_error() {
    local exit_code=$1
    local line_number=$2
    echo "ERROR: <PERSON><PERSON><PERSON> failed with exit code $exit_code at line $line_number"
    echo "Attempting to provide helpful error information..."

    # Check common issues
    if ! command -v python3 &> /dev/null; then
        echo "ERROR: python3 not found. Please install Python 3."
    fi

    if [ ! -d "/opt/homebrew/lib" ]; then
        echo "WARNING: Homebrew not found at /opt/homebrew. You may need to install dependencies."
    fi

    exit $exit_code
}

# Debug: Print the command line arguments
echo "<PERSON>ript called with arguments: $@"
echo "Script name: $0"

# Get the directory where this script is located
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"
APP_DIR="$(dirname "$(dirname "$SCRIPT_DIR")")"
CONTENTS_DIR="$APP_DIR/Contents"
RESOURCES_DIR="$CONTENTS_DIR/Resources"

echo "Script dir: $SCRIPT_DIR"
echo "Resources dir: $RESOURCES_DIR"

# Validate critical directories exist
if [ ! -d "$RESOURCES_DIR" ]; then
    echo "ERROR: Resources directory not found: $RESOURCES_DIR"
    exit 1
fi

if [ ! -d "$RESOURCES_DIR/pynicotine" ]; then
    echo "ERROR: pynicotine module not found in: $RESOURCES_DIR"
    exit 1
fi

# Set up environment variables for GTK and Python with enhanced stability
export PKG_CONFIG_PATH="/opt/homebrew/lib/pkgconfig:/opt/homebrew/share/pkgconfig:$PKG_CONFIG_PATH"
export PYTHONPATH="/opt/homebrew/lib/python3.12/site-packages:$RESOURCES_DIR:$PYTHONPATH"
export DYLD_LIBRARY_PATH="/opt/homebrew/lib:$DYLD_LIBRARY_PATH"
export GI_TYPELIB_PATH="/opt/homebrew/lib/girepository-1.0:$GI_TYPELIB_PATH"
export NICOTINE_GTK_VERSION=3

# macOS-specific stability settings
export OBJC_DISABLE_INITIALIZE_FORK_SAFETY=YES
export PYTHONUNBUFFERED=1
export PYTHONDONTWRITEBYTECODE=1

# Multiprocessing settings for macOS stability
export MULTIPROCESSING_START_METHOD=fork

# GTK-specific fixes for macOS cursor crashes
export GDK_BACKEND=quartz
export GTK_CSD=0
export GDK_SCALE=1
export GDK_DPI_SCALE=1

# Disable problematic GTK features on macOS
export GTK_OVERLAY_SCROLLING=0
export GTK_USE_PORTAL=0

# OpenGL fixes for macOS
export MESA_GL_VERSION_OVERRIDE=3.3
export MESA_GLSL_VERSION_OVERRIDE=330

# Signal handling fixes for macOS
export PYTHONFAULTHANDLER=1
export PYTHONDEVMODE=0

# Threading and signal fixes
export NSAppSleepDisabled=YES
export MACOS_DISABLE_SIGNALS=1

# Set display for GUI
export DISPLAY=:0

# Change to the Resources directory so Python can find the pynicotine module
cd "$RESOURCES_DIR" || {
    echo "ERROR: Failed to change to Resources directory"
    exit 1
}
echo "Changed to directory: $(pwd)"

# Verify Python can import required modules
echo "Verifying Python environment..."
python3 -c "
import sys
print(f'Python version: {sys.version}')
try:
    import gi
    print('✓ PyGObject (gi) available')
except ImportError as e:
    print(f'✗ PyGObject import failed: {e}')
    sys.exit(1)

try:
    gi.require_version('Gtk', '3.0')
    from gi.repository import Gtk
    print('✓ GTK 3.0 available')
except Exception as e:
    print(f'✗ GTK 3.0 import failed: {e}')
    sys.exit(1)

try:
    import pynicotine
    print('✓ pynicotine module available')
except ImportError as e:
    print(f'✗ pynicotine import failed: {e}')
    sys.exit(1)
" || {
    echo "ERROR: Python environment validation failed"
    exit 1
}

# Launch Nicotine Plus Plus with crash recovery
echo "Launching: python3 -m pynicotine"

# Attempt to launch with retry logic
MAX_RETRIES=3
RETRY_COUNT=0

while [ $RETRY_COUNT -lt $MAX_RETRIES ]; do
    echo "Launch attempt $((RETRY_COUNT + 1))/$MAX_RETRIES"

    if python3 -m pynicotine "$@"; then
        echo "Application exited normally"
        exit 0
    else
        EXIT_CODE=$?
        echo "Application crashed with exit code: $EXIT_CODE"

        RETRY_COUNT=$((RETRY_COUNT + 1))

        if [ $RETRY_COUNT -lt $MAX_RETRIES ]; then
            echo "Waiting 2 seconds before retry..."
            sleep 2
        else
            echo "Maximum retries reached. Application failed to start."
            exit $EXIT_CODE
        fi
    fi
done
