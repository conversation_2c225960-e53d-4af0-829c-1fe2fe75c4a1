#!/bin/bash

# Nicotine Plus Plus - macOS Application Launcher
# Enhanced Soulseek client with bulk search functionality

# Debug: Print the command line arguments
echo "Script called with arguments: $@"
echo "Script name: $0"

# Get the directory where this script is located
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"
APP_DIR="$(dirname "$(dirname "$SCRIPT_DIR")")"
CONTENTS_DIR="$APP_DIR/Contents"
RESOURCES_DIR="$CONTENTS_DIR/Resources"

echo "Script dir: $SCRIPT_DIR"
echo "Resources dir: $RESOURCES_DIR"

# Set up environment variables for GTK and Python
export PKG_CONFIG_PATH="/opt/homebrew/lib/pkgconfig:/opt/homebrew/share/pkgconfig:$PKG_CONFIG_PATH"
export PYTHONPATH="/opt/homebrew/lib/python3.12/site-packages:$RESOURCES_DIR:$PYTHONPATH"
export DYLD_LIBRARY_PATH="/opt/homebrew/lib:$DYLD_LIBRARY_PATH"
export GI_TYPELIB_PATH="/opt/homebrew/lib/girepository-1.0:$GI_TYPELIB_PATH"
export NICOTINE_GTK_VERSION=3

# Set display for GUI
export DISPLAY=:0

# Change to the Resources directory so Python can find the pynicotine module
cd "$RESOURCES_DIR"
echo "Changed to directory: $(pwd)"

# Launch Nicotine Plus Plus
echo "Launching: python3 -m pynicotine"
exec python3 -m pynicotine
