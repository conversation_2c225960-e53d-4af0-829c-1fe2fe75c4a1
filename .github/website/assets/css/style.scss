---
---

/*
  SPDX-FileCopyrightText: 2024-2025 <PERSON><PERSON>+ Contributors
  SPDX-License-Identifier: GPL-3.0-or-later
*/

@import "{{ site.theme }}";

/* Link underlines for accessibility */
a:not(h1 a) {
    text-decoration: underline;
    text-underline-offset: .2rem;
}

/* Syntax highlighting (updated colors with better contrast)
   Generated with command 'rougify style github.light' */

.highlight .k,
.highlight .kd,
.highlight .kn,
.highlight .kp,
.highlight .kr,
.highlight .kt,
.highlight .kv {
    color: #cf222e;
}

.highlight .gr {
    color: #f6f8fa;
}

.highlight .gd {
    color: #82071e;
    background-color: #ffebe9;
}

.highlight .nb {
    color: #953800;
}

.highlight .nc {
    color: #953800;
}

.highlight .no {
    color: #953800;
}

.highlight .nn {
    color: #953800;
}

.highlight .sr {
    color: #116329;
}

.highlight .na {
    color: #116329;
}

.highlight .nt {
    color: #116329;
}

.highlight .gi {
    color: #116329;
    background-color: #dafbe1;
}

.highlight .ges {
    font-weight: bold;
    font-style: italic;
}

.highlight .kc {
    color: #0550ae;
}

.highlight .l,
.highlight .ld,
.highlight .m,
.highlight .mb,
.highlight .mf,
.highlight .mh,
.highlight .mi,
.highlight .il,
.highlight .mo,
.highlight .mx {
    color: #0550ae;
}

.highlight .sb {
    color: #0550ae;
}

.highlight .bp {
    color: #0550ae;
}

.highlight .ne {
    color: #0550ae;
}

.highlight .nl {
    color: #0550ae;
}

.highlight .py {
    color: #0550ae;
}

.highlight .nv,
.highlight .vc,
.highlight .vg,
.highlight .vi,
.highlight .vm {
    color: #0550ae;
}

.highlight .o,
.highlight .ow {
    color: #0550ae;
}

.highlight .gh {
    color: #0550ae;
    font-weight: bold;
}

.highlight .gu {
    color: #0550ae;
    font-weight: bold;
}

.highlight .s,
.highlight .sa,
.highlight .sc,
.highlight .dl,
.highlight .sd,
.highlight .s2,
.highlight .se,
.highlight .sh,
.highlight .sx,
.highlight .s1,
.highlight .ss {
    color: #0a3069;
}

.highlight .nd {
    color: #8250df;
}

.highlight .nf,
.highlight .fm {
    color: #8250df;
}

.highlight .err {
    color: #f6f8fa;
    background-color: #82071e;
}

.highlight .c,
.highlight .ch,
.highlight .cd,
.highlight .cm,
.highlight .cp,
.highlight .cpf,
.highlight .c1,
.highlight .cs {
    color: #6e7781;
}

.highlight .gl {
    color: #6e7781;
}

.highlight .gt {
    color: #6e7781;
}

.highlight .ni {
    color: #24292f;
}

.highlight .si {
    color: #24292f;
}

.highlight .ge {
    color: #24292f;
    font-style: italic;
}

.highlight .gs {
    color: #24292f;
    font-weight: bold;
}

/* Dark mode for 'Primer' Jekyll theme */
@media (prefers-color-scheme: dark) {
    body {
        background-color: $black;
        color: $gray-100;
    }

    a {
        color: $blue-300;
    }

    .markdown-body h1,
    .markdown-body h2,
    .markdown-body table th,
    .markdown-body table td,
    .markdown-body table tr {
        border-color: $gray-800;
    }

    .markdown-body blockquote {
        border-color: $gray-600;
        color: $gray-400;
    }

    .markdown-body img,
    .markdown-body table tr {
        background-color: $black;
    }

    .highlight,
    .markdown-body .highlight pre,
    .markdown-body pre,
    .markdown-body table tr:nth-child(2n)  {
        background-color: $gray-900;
    }

    .markdown-body code,
    .markdown-body tt {
        background-color: $white-fade-15;
    }

    /* Syntax highlighting
       Generated with command 'rougify style github.dark' */

    .highlight .k,
    .highlight .kd,
    .highlight .kn,
    .highlight .kp,
    .highlight .kr,
    .highlight .kt,
    .highlight .kv {
        color: #ff7b72;
    }

    .highlight .gr {
        color: #f0f6fc;
    }

    .highlight .gd {
        color: #ffdcd7;
        background-color: #67060c;
    }

    .highlight .nb {
        color: #ffa657;
    }

    .highlight .nc {
        color: #ffa657;
    }

    .highlight .no {
        color: #ffa657;
    }

    .highlight .nn {
        color: #ffa657;
    }

    .highlight .sr {
        color: #7ee787;
    }

    .highlight .na {
        color: #7ee787;
    }

    .highlight .nt {
        color: #7ee787;
    }

    .highlight .gi {
        color: #aff5b4;
        background-color: #033a16;
    }

    .highlight .ges {
        font-weight: bold;
        font-style: italic;
    }

    .highlight .kc {
        color: #79c0ff;
    }

    .highlight .l,
    .highlight .ld,
    .highlight .m,
    .highlight .mb,
    .highlight .mf,
    .highlight .mh,
    .highlight .mi,
    .highlight .il,
    .highlight .mo,
    .highlight .mx {
        color: #79c0ff;
    }

    .highlight .sb {
        color: #79c0ff;
    }

    .highlight .bp {
        color: #79c0ff;
    }

    .highlight .ne {
        color: #79c0ff;
    }

    .highlight .nl {
        color: #79c0ff;
    }

    .highlight .py {
        color: #79c0ff;
    }

    .highlight .nv,
    .highlight .vc,
    .highlight .vg,
    .highlight .vi,
    .highlight .vm {
        color: #79c0ff;
    }

    .highlight .o,
    .highlight .ow {
        color: #79c0ff;
    }

    .highlight .gh {
        color: #1f6feb;
        font-weight: bold;
    }

    .highlight .gu {
        color: #1f6feb;
        font-weight: bold;
    }

    .highlight .s,
    .highlight .sa,
    .highlight .sc,
    .highlight .dl,
    .highlight .sd,
    .highlight .s2,
    .highlight .se,
    .highlight .sh,
    .highlight .sx,
    .highlight .s1,
    .highlight .ss {
        color: #a5d6ff;
    }

    .highlight .nd {
        color: #d2a8ff;
    }

    .highlight .nf,
    .highlight .fm {
        color: #d2a8ff;
    }

    .highlight .err {
        color: #f0f6fc;
        background-color: #8e1519;
    }

    .highlight .c,
    .highlight .ch,
    .highlight .cd,
    .highlight .cm,
    .highlight .cp,
    .highlight .cpf,
    .highlight .c1,
    .highlight .cs {
        color: #8b949e;
    }

    .highlight .gl {
        color: #8b949e;
    }

    .highlight .gt {
        color: #8b949e;
    }

    .highlight .ni {
        color: #c9d1d9;
    }

    .highlight .si {
        color: #c9d1d9;
    }

    .highlight .ge {
        color: #c9d1d9;
        font-style: italic;
    }

    .highlight .gs {
        color: #c9d1d9;
        font-weight: bold;
    }
}
