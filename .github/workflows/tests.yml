# SPDX-FileCopyrightText: 2020-2025 <PERSON><PERSON>+ Contributors
# SPDX-License-Identifier: GPL-3.0-or-later

name: Tests
on: [push, pull_request]
permissions: {}

env:
  NICOTINE_NETWORK_TESTS: 1

jobs:

  python:
    runs-on: ubuntu-24.04
    timeout-minutes: 15
    strategy:
      fail-fast: false
      matrix:
        python: ['3.9', '3.10', '3.11', '3.12', '3.13', '3.14-dev',
                 'pypy-3.10']
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Python ${{ matrix.python }}
        uses: actions/setup-python@v5
        with:
          python-version: ${{ matrix.python }}
          cache: 'pip'
          cache-dependency-path: setup.py

      - name: Install dependencies
        run: |
          sudo apt update
          sudo apt install gettext gir1.2-gtk-4.0 libcairo2-dev \
            libgirepository-2.0-dev libgtk-4-bin
          python -m pip install build setuptools wheel .[tests]

      - name: PEP 8 style checks
        run: python -m pycodestyle

      - name: Linting
        run: python -m pylint --recursive=y .

      - name: Integration and unit tests
        run: python -m unittest -v

      - name: Build
        run: python -m build

      - name: Build without isolation
        run: python3 -m build --no-isolation

  ubuntu-debian:
    runs-on: ubuntu-latest
    timeout-minutes: 15
    strategy:
      fail-fast: false
      matrix:
        container: ['debian:bullseye', 'debian:bookworm', 'debian:unstable',
                    'ubuntu:jammy', 'ubuntu:noble', 'ubuntu:devel']
    container: ${{ matrix.container }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Install autopkgtest dependencies
        run: |
          apt update
          apt -y install autopkgtest

      - name: Run autopkgtest
        run: autopkgtest . -- null

  fedora:
    timeout-minutes: 15
    strategy:
      fail-fast: false
      matrix:
        container: ['fedora:rawhide']
        gtk: [4, 3]
        platform: ['ubuntu-latest', 'ubuntu-24.04-arm']
    runs-on: ${{ matrix.platform }}
    container: ${{ matrix.container }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Install dependencies
        run: |
          dnf -y install gettext gtk${{ matrix.gtk }} pylint python3 python3-build \
            python3-gobject python3-pycodestyle python3-setuptools python3-wheel

      - name: PEP 8 style checks
        run: python3 -m pycodestyle

      - name: Linting
        run: python3 -m pylint --recursive=y .

      - name: Integration and unit tests
        run: python3 -m unittest -v

      - name: Build
        run: python3 -m build

      - name: Build without isolation
        run: python3 -m build --no-isolation

  opensuse:
    runs-on: ubuntu-latest
    timeout-minutes: 15
    strategy:
      fail-fast: false
      matrix:
        container: ['opensuse/tumbleweed']
    container: ${{ matrix.container }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Install dependencies
        run: |
          zypper refresh
          zypper -n install dejavu-fonts gettext-tools python313-build python313-gobject \
            python313-gobject-Gdk python313-pycodestyle python313-pylint \
            python313-setuptools python313-wheel typelib-1_0-Gtk-4_0

      - name: PEP 8 style checks
        run: python3.13 -m pycodestyle

      - name: Linting
        run: python3.13 -m pylint --recursive=y .

      - name: Integration and unit tests
        run: python3.13 -m unittest -v

      - name: Build
        run: python3.13 -m build

      - name: Build without isolation
        run: python3.13 -m build --no-isolation

  alpine:
    runs-on: ubuntu-latest
    timeout-minutes: 15
    strategy:
      fail-fast: false
      matrix:
        container: ['alpine:edge']
    container: ${{ matrix.container }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Install dependencies
        run: |
          apk update
          apk add font-dejavu gettext gtk4.0 py3-build py3-gobject3 py3-pycodestyle \
            py3-pylint py3-setuptools py3-wheel

      - name: PEP 8 style checks
        run: python3 -m pycodestyle

      - name: Linting
        run: python3 -m pylint --recursive=y .

      - name: Integration and unit tests
        run: python3 -m unittest -v

      - name: Build
        run: python3 -m build

      - name: Build without isolation
        run: python3 -m build --no-isolation

  windows:
    timeout-minutes: 15
    strategy:
      fail-fast: false
      matrix:
        include:
          - arch: x86_64
            prefix: mingw-w64-clang-x86_64
            msystem: clang64
            platform: windows-latest
          - arch: arm64
            prefix: mingw-w64-clang-aarch64
            msystem: clangarm64
            platform: windows-11-arm
    runs-on: ${{ matrix.platform }}
    env:
      ARCH: ${{ matrix.arch }}
    defaults:
      run:
        shell: msys2 {0}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup msys2
        uses: msys2/setup-msys2@v2
        with:
          msystem: ${{ matrix.msystem }}
          update: true
          install: >-
            ${{ matrix.prefix }}-ca-certificates
            ${{ matrix.prefix }}-gettext-tools
            ${{ matrix.prefix }}-gtk4
            ${{ matrix.prefix }}-libadwaita
            ${{ matrix.prefix }}-python-build
            ${{ matrix.prefix }}-python-cx-freeze
            ${{ matrix.prefix }}-python-gobject
            ${{ matrix.prefix }}-python-pycodestyle
            ${{ matrix.prefix }}-python-pylint
            ${{ matrix.prefix }}-python-setuptools
            ${{ matrix.prefix }}-python-wheel
            ${{ matrix.prefix }}-webp-pixbuf-loader

      - name: Install additional dependencies
        run: python3 build-aux/windows/dependencies.py

      - name: PEP 8 style checks
        run: python3 -m pycodestyle

      - name: Linting
        run: python3 -m pylint --recursive=y .

      - name: Integration and unit tests
        run: python3 -m unittest -v

      - name: Build without isolation
        run: python3 -m build --no-isolation

  macos:
    strategy:
      fail-fast: false
      matrix:
        include:
          - arch: x86_64
            platform: macos-13
          - arch: arm64
            platform: macos-14
    runs-on: ${{ matrix.platform }}
    timeout-minutes: 20
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set system language
        run: defaults write NSGlobalDomain AppleLanguages "(en-US)"

      - name: Create Python virtual environment
        run: |
          brew uninstall --ignore-dependencies python@3.13
          brew install --overwrite python@3.13
          python3.13 -m venv venv

      - name: Install dependencies
        run: venv/bin/python3 build-aux/macos/dependencies.py

      - name: PEP 8 style checks
        run: venv/bin/python3 -m pycodestyle

      - name: Linting
        run: venv/bin/python3 -m pylint --recursive=y .

      - name: Integration and unit tests
        run: venv/bin/python3 -m unittest -v

      - name: Build
        run: venv/bin/python3 -m build

      - name: Build without isolation
        run: venv/bin/python3 -m build --no-isolation
