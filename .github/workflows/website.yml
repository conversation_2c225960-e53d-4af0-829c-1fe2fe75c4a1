# SPDX-FileCopyrightText: 2024-2025 <PERSON><PERSON>+ Contributors
# SPDX-License-Identifier: GPL-3.0-or-later

name: Website

on:
  push:
    branches: ['master']
    paths: ['**.md']
  workflow_dispatch:

# Sets permissions of the GITHUB_TOKEN to allow deployment to GitHub Pages
permissions:
  contents: read
  pages: write
  id-token: write

# Allow only one concurrent deployment, skipping runs queued between the run in-progress and latest queued.
# However, do NOT cancel in-progress runs as we want to allow these production deployments to complete.
concurrency:
  group: 'pages'
  cancel-in-progress: false

jobs:
  build:
    if: github.repository == 'nicotine-plus/nicotine-plus'
    runs-on: ubuntu-latest
    timeout-minutes: 15
    env:
      WEBSITE_ROOT: .github/website
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Setup Pages
        uses: actions/configure-pages@v5

      - name: Add Pages
        run: |
          mkdir -p $WEBSITE_ROOT/data/icons/
          cp -r data/screenshots $WEBSITE_ROOT/data/
          cp -r doc $WEBSITE_ROOT/
          cp AUTHORS.md $WEBSITE_ROOT/
          cp README.md $WEBSITE_ROOT/
          cp NEWS.md $WEBSITE_ROOT/
          cp data/icons/icon.svg $WEBSITE_ROOT/data/icons/

      - name: Strip headers
        run: find $WEBSITE_ROOT -name '*.md' -exec sed -i '/<!--/,/-->/d' {} +

      - name: Build with Jekyll
        uses: actions/jekyll-build-pages@v1
        with:
          source: ${{ env.WEBSITE_ROOT }}/
          destination: _site

      - name: Upload artifact
        uses: actions/upload-pages-artifact@v3

  deploy:
    environment:
      name: github-pages
      url: ${{ steps.deployment.outputs.page_url }}
    runs-on: ubuntu-latest
    timeout-minutes: 15
    needs: build
    steps:
      - name: Deploy to GitHub Pages
        id: deployment
        uses: actions/deploy-pages@v4
