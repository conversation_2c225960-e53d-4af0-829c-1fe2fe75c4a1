# SPDX-FileCopyrightText: 2020-2025 <PERSON><PERSON>+ Contributors
# SPDX-License-Identifier: GPL-3.0-or-later

name: Packaging
on: [push, pull_request]
permissions: {}

jobs:

  debian:
    runs-on: ubuntu-latest
    timeout-minutes: 15
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Install dependencies
        run: |
          sudo apt update
          sudo apt install python3-build
          sudo apt build-dep .

      - name: Build Debian package
        run: |
          python3 -m build --sdist --no-isolation
          mk-origtargz dist/nicotine-plus-*.tar.gz
          debuild -sa -us -uc

      - name: Prepare artifacts
        run: |
          mkdir build/package/
          cp -Lr ../nicotine_* build/package/

      - name: Archive artifacts
        uses: actions/upload-artifact@v4
        with:
          name: debian-package
          path: build/package/

  flatpak:
    runs-on: ubuntu-latest
    timeout-minutes: 15
    strategy:
      matrix:
        include:
          - arch: x86_64
            platform: ubuntu-latest
    container:
      image: ghcr.io/flathub-infra/flatpak-github-actions:gnome-48
      options: --privileged
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Validate manifest
        run: flatpak-builder-lint --exceptions manifest build-aux/flatpak/org.nicotine_plus.Nicotine.json

      - name: Validate appstream
        run: flatpak-builder-lint appstream data/org.nicotine_plus.Nicotine.appdata.xml.in

      - name: Build Flatpak package
        uses: flatpak/flatpak-github-actions/flatpak-builder@v6
        with:
          bundle: org.nicotine_plus.Nicotine-${{ matrix.arch }}.flatpak
          manifest-path: build-aux/flatpak/org.nicotine_plus.Nicotine.json
          cache-key: flatpak-builder-${{ github.sha }}
          arch: ${{ matrix.arch }}
          upload-artifact: false

      - name: Archive artifacts
        uses: actions/upload-artifact@v4
        with:
          name: flatpak-${{ matrix.arch }}-package
          path: org.nicotine_plus.Nicotine-${{ matrix.arch }}.flatpak

  snap:
    timeout-minutes: 15
    strategy:
      matrix:
        include:
          - arch: x86_64
            platform: ubuntu-latest
          - arch: arm64
            platform: ubuntu-24.04-arm
    runs-on: ${{ matrix.platform }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Build Snap package
        uses: snapcore/action-build@v1
        id: build-snap

      - name: Install Snap package
        run: sudo snap install --dangerous ${{ steps.build-snap.outputs.snap }}

      - name: Archive artifacts
        uses: actions/upload-artifact@v4
        with:
          name: snap-${{ matrix.arch }}-package
          path: ${{ steps.build-snap.outputs.snap }}

  windows:
    timeout-minutes: 15
    strategy:
      matrix:
        include:
          - arch: x86_64
            prefix: mingw-w64-clang-x86_64
            msystem: clang64
            platform: windows-latest
          - arch: arm64
            prefix: mingw-w64-clang-aarch64
            msystem: clangarm64
            platform: windows-11-arm
    runs-on: ${{ matrix.platform }}
    env:
      ARCH: ${{ matrix.arch }}
    defaults:
      run:
        shell: msys2 {0}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup msys2
        uses: msys2/setup-msys2@v2
        with:
          msystem: ${{ matrix.msystem }}
          update: true
          install: >-
            ${{ matrix.prefix }}-ca-certificates
            ${{ matrix.prefix }}-gettext-tools
            ${{ matrix.prefix }}-gtk4
            ${{ matrix.prefix }}-libadwaita
            ${{ matrix.prefix }}-python-build
            ${{ matrix.prefix }}-python-cx-freeze
            ${{ matrix.prefix }}-python-gobject
            ${{ matrix.prefix }}-python-pycodestyle
            ${{ matrix.prefix }}-python-pylint
            ${{ matrix.prefix }}-python-setuptools
            ${{ matrix.prefix }}-python-wheel
            ${{ matrix.prefix }}-webp-pixbuf-loader

      - name: Install additional dependencies
        run: python3 build-aux/windows/dependencies.py

      - name: Freeze application
        run: python3 build-aux/windows/setup.py bdist_msi

      - name: Archive installer artifacts
        uses: actions/upload-artifact@v4
        with:
          name: windows-${{ matrix.arch }}-installer
          path: build-aux/windows/build/*.msi

      - name: Archive package artifacts
        uses: actions/upload-artifact@v4
        with:
          name: windows-${{ matrix.arch }}-package
          path: build-aux/windows/build/package

  macos:
    strategy:
      matrix:
        include:
          - arch: x86_64
            platform: macos-13
          - arch: arm64
            platform: macos-14
    runs-on: ${{ matrix.platform }}
    timeout-minutes: 20
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set system language
        run: defaults write NSGlobalDomain AppleLanguages "(en-US)"

      - name: Create Python virtual environment
        run: |
          brew uninstall --ignore-dependencies python@3.13
          brew install --overwrite python@3.13
          python3.13 -m venv venv

      - name: Install build dependencies
        run: venv/bin/python3 build-aux/macos/dependencies.py

      - name: Freeze application
        run: venv/bin/python3 build-aux/macos/setup.py bdist_dmg

      - name: Archive installer artifacts
        uses: actions/upload-artifact@v4
        with:
          name: macos-${{ matrix.arch }}-installer
          path: build-aux/macos/build/*.dmg
